import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';

/**
 * GET /api/user/profile
 * Get current user's profile information
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get user profile with company information
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select(`
        id,
        email,
        full_name,
        avatar_url,
        company_id,
        role,
        is_company_admin,
        disabled,
        last_login_at,
        timezone,
        created_at,
        updated_at,
        company:company_id(
          id,
          name,
          slug,
          logo_url,
          description,
          subscription_plan,
          subscription_status,
          max_users,
          max_records,
          disabled
        )
      `)
      .eq('email', userEmail)
      .single();

    if (profileError) {
      if (profileError.code === 'PGRST116') {
        // Profile not found, create it
        console.log('Profile not found for user:', userEmail, 'Creating new profile...');

        const { data: newProfile, error: createError } = await supabase
          .from('profiles')
          .insert({
            email: userEmail,
            full_name: session.user.name || '',
            avatar_url: session.user.image || '',
            role: 'user',
            is_company_admin: false,
            disabled: false,
            timezone: 'UTC'
          })
          .select(`
            id,
            email,
            full_name,
            avatar_url,
            company_id,
            role,
            is_company_admin,
            disabled,
            last_login_at,
            timezone,
            created_at,
            updated_at,
            company:company_id(
              id,
              name,
              slug,
              logo_url,
              description,
              subscription_plan,
              subscription_status,
              max_users,
              max_records,
              disabled
            )
          `)
          .single();

        if (createError) {
          console.error('Error creating user profile:', createError);
          return NextResponse.json({ error: 'Failed to create user profile' }, { status: 500 });
        }

        console.log('User profile created successfully:', newProfile);

        // Update last login timestamp for the new profile
        await supabase
          .from('profiles')
          .update({ last_login_at: new Date().toISOString() })
          .eq('id', newProfile.id);

        return NextResponse.json({
          data: newProfile
        });
      }
      console.error('Error fetching user profile:', profileError);
      return NextResponse.json({ error: 'Failed to fetch profile' }, { status: 500 });
    }

    // Update last login timestamp
    await supabase
      .from('profiles')
      .update({ last_login_at: new Date().toISOString() })
      .eq('id', profile.id);

    return NextResponse.json({
      data: profile
    });

  } catch (error) {
    console.error('User profile GET API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PATCH /api/user/profile
 * Update current user's profile information
 */
export async function PATCH(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get current profile
    const { data: currentProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('id, disabled')
      .eq('email', userEmail)
      .single();

    if (fetchError || !currentProfile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (currentProfile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    const body = await request.json();
    const { full_name, timezone } = body;

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (full_name !== undefined) {
      updateData.full_name = full_name?.trim() || null;
    }

    if (timezone !== undefined) {
      updateData.timezone = timezone || 'UTC';
    }

    // Update the profile
    const { data: updatedProfile, error: updateError } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', currentProfile.id)
      .select(`
        id,
        email,
        full_name,
        avatar_url,
        company_id,
        role,
        is_company_admin,
        disabled,
        last_login_at,
        timezone,
        created_at,
        updated_at,
        company:company_id(
          id,
          name,
          slug,
          logo_url,
          description,
          subscription_plan,
          subscription_status,
          max_users,
          max_records,
          disabled
        )
      `)
      .single();

    if (updateError) {
      console.error('Error updating profile:', updateError);
      return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 });
    }

    return NextResponse.json({
      data: updatedProfile,
      message: 'Profile updated successfully'
    });

  } catch (error) {
    console.error('User profile PATCH API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
