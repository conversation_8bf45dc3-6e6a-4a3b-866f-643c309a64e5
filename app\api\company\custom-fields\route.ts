import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';
import { createAuditLog } from 'src/lib/auditLog';

/**
 * GET /api/company/custom-fields
 * Get company's custom fields
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, is_company_admin, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company' }, { status: 403 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const includeInactive = searchParams.get('include_inactive') === 'true';

    // Build query
    let query = supabase
      .from('custom_fields')
      .select(`
        id,
        created_at,
        updated_at,
        name,
        field_key,
        field_type,
        description,
        is_required,
        is_active,
        default_value,
        validation_rules,
        dropdown_options,
        display_order,
        created_by,
        updated_by
      `)
      .eq('company_id', profile.company_id)
      .order('display_order', { ascending: true });

    // Filter by active status if not including inactive
    if (!includeInactive) {
      query = query.eq('is_active', true);
    }

    const { data: customFields, error: fieldsError } = await query;

    if (fieldsError) {
      console.error('Error fetching custom fields:', fieldsError);
      return NextResponse.json({ error: 'Failed to fetch custom fields' }, { status: 500 });
    }

    return NextResponse.json({
      data: customFields || []
    });

  } catch (error) {
    console.error('Custom fields GET API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/company/custom-fields
 * Create a new custom field
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, is_company_admin, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company' }, { status: 403 });
    }

    if (!profile.is_company_admin) {
      return NextResponse.json({ error: 'Company admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const {
      name,
      field_key,
      field_type,
      description,
      is_required = false,
      is_active = true,
      default_value,
      validation_rules = {},
      dropdown_options,
      display_order = 0
    } = body;

    // Validation
    if (!name || !field_key || !field_type) {
      return NextResponse.json({ error: 'Name, field key, and field type are required' }, { status: 400 });
    }

    if (!/^[a-z0-9_]+$/.test(field_key)) {
      return NextResponse.json({ 
        error: 'Field key must contain only lowercase letters, numbers, and underscores' 
      }, { status: 400 });
    }

    const validFieldTypes = ['text', 'number', 'dropdown', 'checkbox', 'date', 'email', 'phone', 'price'];
    if (!validFieldTypes.includes(field_type)) {
      return NextResponse.json({ error: 'Invalid field type' }, { status: 400 });
    }

    // Check if field key already exists in this company
    const { data: existingField } = await supabase
      .from('custom_fields')
      .select('id')
      .eq('company_id', profile.company_id)
      .eq('field_key', field_key)
      .single();

    if (existingField) {
      return NextResponse.json({ error: 'Field key already exists in this company' }, { status: 409 });
    }

    // Create the custom field
    const { data: newField, error: createError } = await supabase
      .from('custom_fields')
      .insert({
        company_id: profile.company_id,
        name,
        field_key,
        field_type,
        description: description || null,
        is_required,
        is_active,
        default_value: default_value || null,
        validation_rules,
        dropdown_options,
        display_order,
        created_by: profile.id,
        updated_by: profile.id
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating custom field:', createError);
      return NextResponse.json({ error: 'Failed to create custom field' }, { status: 500 });
    }

    // Create audit log
    await createAuditLog({
      user_id: profile.id,
      user_email: userEmail,
      company_id: profile.company_id,
      action: 'CREATE',
      resource_type: 'custom_field',
      resource_id: newField.id,
      new_values: newField,
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      user_agent: request.headers.get('user-agent')
    });

    return NextResponse.json({
      data: newField,
      message: 'Custom field created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Custom fields POST API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
