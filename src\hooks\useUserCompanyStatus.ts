import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

export interface UserCompanyStatus {
  hasCompany: boolean;
  isCompanyAdmin: boolean;
  companyId: string | null;
  companyName: string | null;
  pendingInvitations: any[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useUserCompanyStatus(): UserCompanyStatus {
  const { data: session } = useSession();
  const [status, setStatus] = useState<UserCompanyStatus>({
    hasCompany: false,
    isCompanyAdmin: false,
    companyId: null,
    companyName: null,
    pendingInvitations: [],
    loading: true,
    error: null,
    refetch: () => {}
  });

  const fetchStatus = async () => {
    if (!session?.user?.email) {
      setStatus(prev => ({ ...prev, loading: false }));
      return;
    }

    try {
      setStatus(prev => ({ ...prev, loading: true, error: null }));

      // Fetch user profile and pending invitations in parallel
      const [profileResponse, invitationsResponse] = await Promise.all([
        fetch('/api/user/profile'),
        fetch('/api/user/invitations')
      ]);

      if (!profileResponse.ok) {
        throw new Error('Failed to fetch user profile');
      }

      const profileData = await profileResponse.json();
      const profile = profileData.data;

      let pendingInvitations = [];
      if (invitationsResponse.ok) {
        const invitationsData = await invitationsResponse.json();
        pendingInvitations = invitationsData.data || [];
      }

      setStatus(prev => ({
        ...prev,
        hasCompany: !!profile.company_id,
        isCompanyAdmin: profile.is_company_admin || false,
        companyId: profile.company_id,
        companyName: profile.company?.name || null,
        pendingInvitations,
        loading: false,
        error: null
      }));

    } catch (error) {
      console.error('Error fetching user company status:', error);
      setStatus(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch status'
      }));
    }
  };

  useEffect(() => {
    fetchStatus();
  }, [session?.user?.email]);

  return {
    ...status,
    refetch: fetchStatus
  };
}
