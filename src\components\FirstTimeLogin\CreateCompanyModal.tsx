'use client';

import { useState, useEffect } from 'react';
import {
  Modal,
  Stack,
  TextInput,
  Textarea,
  Button,
  Group,
  Text,
  Alert,
  LoadingOverlay,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { useDebouncedValue } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconBuilding,
  IconCheck,
  IconAlertCircle,
  IconX,
} from '@tabler/icons-react';

interface CreateCompanyModalProps {
  opened: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface CreateCompanyForm {
  name: string;
  slug: string;
  description: string;
}

export function CreateCompanyModal({ opened, onClose, onSuccess }: CreateCompanyModalProps) {
  const [loading, setLoading] = useState(false);
  const [slugChecking, setSlugChecking] = useState(false);
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);
  const [slugError, setSlugError] = useState<string | null>(null);

  const form = useForm<CreateCompanyForm>({
    initialValues: {
      name: '',
      slug: '',
      description: '',
    },
    validate: {
      name: (value) => {
        if (!value || value.trim().length === 0) {
          return 'Company name is required';
        }
        if (value.trim().length < 2) {
          return 'Company name must be at least 2 characters';
        }
        return null;
      },
      slug: (value) => {
        if (!value || value.trim().length === 0) {
          return 'Company slug is required';
        }
        if (value.trim().length < 2) {
          return 'Slug must be at least 2 characters';
        }
        const slugRegex = /^[a-z0-9-_]+$/;
        if (!slugRegex.test(value)) {
          return 'Slug must contain only lowercase letters, numbers, hyphens, and underscores';
        }
        if (slugAvailable === false) {
          return 'This slug is already taken';
        }
        return null;
      },
    },
  });

  const [debouncedSlug] = useDebouncedValue(form.values.slug, 500);

  // Auto-generate slug from company name
  useEffect(() => {
    if (form.values.name && !form.values.slug) {
      const generatedSlug = form.values.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-_]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
      
      form.setFieldValue('slug', generatedSlug);
    }
  }, [form.values.name]);

  // Check slug availability
  useEffect(() => {
    const checkSlugAvailability = async () => {
      if (!debouncedSlug || debouncedSlug.length < 2) {
        setSlugAvailable(null);
        setSlugError(null);
        return;
      }

      const slugRegex = /^[a-z0-9-_]+$/;
      if (!slugRegex.test(debouncedSlug)) {
        setSlugAvailable(false);
        setSlugError('Slug must contain only lowercase letters, numbers, hyphens, and underscores');
        return;
      }

      try {
        setSlugChecking(true);
        setSlugError(null);

        const response = await fetch(`/api/company/create/check-slug?slug=${encodeURIComponent(debouncedSlug)}`);
        const data = await response.json();

        if (response.ok) {
          setSlugAvailable(data.available);
          if (!data.available) {
            setSlugError('This slug is already taken');
          }
        } else {
          setSlugError(data.error || 'Failed to check slug availability');
          setSlugAvailable(false);
        }
      } catch (error) {
        console.error('Error checking slug:', error);
        setSlugError('Failed to check slug availability');
        setSlugAvailable(false);
      } finally {
        setSlugChecking(false);
      }
    };

    checkSlugAvailability();
  }, [debouncedSlug]);

  const handleSubmit = async (values: CreateCompanyForm) => {
    try {
      setLoading(true);

      const response = await fetch('/api/company/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: values.name.trim(),
          slug: values.slug.trim(),
          description: values.description.trim() || undefined,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create company');
      }

      const result = await response.json();

      notifications.show({
        title: 'Success',
        message: `Company "${result.data.company.name}" created successfully!`,
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      form.reset();
      onSuccess();

    } catch (error) {
      console.error('Error creating company:', error);
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to create company',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      form.reset();
      setSlugAvailable(null);
      setSlugError(null);
      onClose();
    }
  };

  const getSlugRightSection = () => {
    if (slugChecking) {
      return <div style={{ width: 16, height: 16 }}>⏳</div>;
    }
    if (slugAvailable === true) {
      return <IconCheck size={16} color="green" />;
    }
    if (slugAvailable === false) {
      return <IconX size={16} color="red" />;
    }
    return null;
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title="Create New Company"
      size="md"
      closeOnClickOutside={!loading}
      closeOnEscape={!loading}
    >
      <div style={{ position: 'relative' }}>
        <LoadingOverlay visible={loading} />
        
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack gap="md">
            <Alert icon={<IconBuilding size={16} />} color="blue">
              You will become the company administrator and can invite team members later.
            </Alert>

            <TextInput
              label="Company Name"
              placeholder="Enter your company name"
              required
              {...form.getInputProps('name')}
            />

            <TextInput
              label="Company Slug"
              placeholder="company-slug"
              description="This will be used in URLs and must be unique"
              required
              rightSection={getSlugRightSection()}
              error={form.errors.slug || slugError}
              {...form.getInputProps('slug')}
            />

            <Textarea
              label="Description"
              placeholder="Brief description of your company (optional)"
              rows={3}
              {...form.getInputProps('description')}
            />

            <Group justify="flex-end" mt="md">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                loading={loading}
                disabled={slugChecking || slugAvailable === false}
                leftSection={<IconBuilding size={16} />}
              >
                Create Company
              </Button>
            </Group>
          </Stack>
        </form>
      </div>
    </Modal>
  );
}
