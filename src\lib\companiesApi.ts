import { Company, CompanyFormData, CompaniesResponse, CompanyFilters } from 'src/types/companies';

const API_BASE = '/api/admin/companies';

export class CompaniesApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = 'CompaniesApiError';
  }
}

async function handleResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new CompaniesApiError(
      errorData.error || `HTTP ${response.status}`,
      response.status,
      errorData.code
    );
  }
  return response.json();
}

/**
 * Fetch companies with filtering and pagination
 */
export async function fetchCompanies(filters: CompanyFilters = {}): Promise<CompaniesResponse> {
  const params = new URLSearchParams();
  
  if (filters.search) params.append('search', filters.search);
  if (filters.subscriptionPlan) params.append('subscriptionPlan', filters.subscriptionPlan);
  if (filters.subscriptionStatus) params.append('subscriptionStatus', filters.subscriptionStatus);
  if (filters.page) params.append('page', filters.page.toString());
  if (filters.limit) params.append('limit', filters.limit.toString());

  const url = `${API_BASE}${params.toString() ? `?${params.toString()}` : ''}`;
  const response = await fetch(url);
  return handleResponse<CompaniesResponse>(response);
}

/**
 * Fetch a single company by ID
 */
export async function fetchCompany(id: string): Promise<{ data: Company }> {
  const response = await fetch(`${API_BASE}/${id}`);
  return handleResponse<{ data: Company }>(response);
}

/**
 * Create a new company
 */
export async function createCompany(data: CompanyFormData): Promise<{ data: Company; message: string }> {
  const response = await fetch(API_BASE, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  return handleResponse<{ data: Company; message: string }>(response);
}

/**
 * Update an existing company
 */
export async function updateCompany(
  id: string, 
  data: Partial<CompanyFormData>
): Promise<{ data: Company; message: string }> {
  const response = await fetch(`${API_BASE}/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  return handleResponse<{ data: Company; message: string }>(response);
}

/**
 * Delete a company
 */
export async function deleteCompany(id: string): Promise<{ message: string }> {
  const response = await fetch(`${API_BASE}/${id}`, {
    method: 'DELETE',
  });
  return handleResponse<{ message: string }>(response);
}

/**
 * Toggle company disabled status
 */
export async function toggleCompanyStatus(id: string, disabled: boolean): Promise<{ data: Company; message: string }> {
  return updateCompany(id, { disabled });
}

/**
 * Update company subscription
 */
export async function updateCompanySubscription(
  id: string,
  subscription_plan: string,
  subscription_status: string,
  max_users?: number,
  max_records?: number
): Promise<{ data: Company; message: string }> {
  const updateData: any = {
    subscription_plan,
    subscription_status
  };
  
  if (max_users !== undefined) updateData.max_users = max_users;
  if (max_records !== undefined) updateData.max_records = max_records;
  
  return updateCompany(id, updateData);
}

/**
 * Bulk update companies
 */
export async function bulkUpdateCompanies(
  updates: { id: string; data: Partial<CompanyFormData> }[]
): Promise<{ success: number; errors: { id: string; error: string }[] }> {
  const results = await Promise.allSettled(
    updates.map(({ id, data }) => updateCompany(id, data))
  );

  const success = results.filter(result => result.status === 'fulfilled').length;
  const errors = results
    .map((result, index) => {
      if (result.status === 'rejected') {
        return {
          id: updates[index].id,
          error: result.reason.message || 'Unknown error'
        };
      }
      return null;
    })
    .filter(Boolean) as { id: string; error: string }[];

  return { success, errors };
}

/**
 * Export companies data
 */
export async function exportCompanies(format: 'csv' | 'json' = 'csv'): Promise<Blob> {
  const { data: companies } = await fetchCompanies({ limit: 1000 }); // Get all companies
  
  if (format === 'json') {
    const jsonData = JSON.stringify(companies, null, 2);
    return new Blob([jsonData], { type: 'application/json' });
  }

  // CSV format
  const headers = [
    'ID',
    'Name',
    'Slug',
    'Domain',
    'Subscription Plan',
    'Subscription Status',
    'Max Users',
    'Max Records',
    'Current Users',
    'Current Records',
    'Disabled',
    'Created At',
    'Updated At'
  ];

  const csvRows = [
    headers.join(','),
    ...companies.map(company => [
      company.id,
      `"${company.name}"`,
      company.slug,
      `"${company.domain || ''}"`,
      company.subscription_plan,
      company.subscription_status,
      company.max_users,
      company.max_records,
      company.user_count || 0,
      company.record_count || 0,
      company.disabled ? 'Yes' : 'No',
      company.created_at,
      company.updated_at || ''
    ].join(','))
  ];

  const csvContent = csvRows.join('\n');
  return new Blob([csvContent], { type: 'text/csv' });
}

/**
 * Download exported data
 */
export function downloadBlob(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * Validate company data before submission
 */
export function validateCompanyData(data: CompanyFormData): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!data.name?.trim()) {
    errors.push('Company name is required');
  }

  if (!data.slug?.trim()) {
    errors.push('Company slug is required');
  } else if (!/^[a-z0-9-]+$/.test(data.slug)) {
    errors.push('Slug must contain only lowercase letters, numbers, and hyphens');
  }

  if (data.domain && !/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/.test(data.domain)) {
    errors.push('Invalid domain format');
  }

  if (data.max_users < 1) {
    errors.push('Maximum users must be at least 1');
  }

  if (data.max_records < 1) {
    errors.push('Maximum records must be at least 1');
  }

  const validPlans = ['free', 'basic', 'premium', 'enterprise'];
  if (!validPlans.includes(data.subscription_plan)) {
    errors.push('Invalid subscription plan');
  }

  const validStatuses = ['active', 'suspended', 'cancelled'];
  if (!validStatuses.includes(data.subscription_status)) {
    errors.push('Invalid subscription status');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get company statistics
 */
export async function getCompanyStatistics(): Promise<{
  total: number;
  active: number;
  suspended: number;
  cancelled: number;
  byPlan: Record<string, number>;
  totalUsers: number;
  totalRecords: number;
}> {
  const { data: companies } = await fetchCompanies({ limit: 1000 });
  
  const stats = {
    total: companies.length,
    active: companies.filter(c => c.subscription_status === 'active').length,
    suspended: companies.filter(c => c.subscription_status === 'suspended').length,
    cancelled: companies.filter(c => c.subscription_status === 'cancelled').length,
    byPlan: companies.reduce((acc, company) => {
      acc[company.subscription_plan] = (acc[company.subscription_plan] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    totalUsers: companies.reduce((sum, company) => sum + (company.user_count || 0), 0),
    totalRecords: companies.reduce((sum, company) => sum + (company.record_count || 0), 0)
  };

  return stats;
}
