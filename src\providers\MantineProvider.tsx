"use client";

import {
  MantineProvider as Provider,
  createTheme,
  MantineThemeOverride
} from "@mantine/core";
import { Notifications } from '@mantine/notifications';
import { ModalsProvider } from '@mantine/modals';
import { ReactNode } from "react";

// Create a custom theme with dark mode
const themeOverride: MantineThemeOverride = {
  primaryColor: "blue",
  fontFamily: "verdana",
  // Other theme customizations
  components: {
    Container: {
      defaultProps: {
        p: "md",
      },
    },
    Title: {
      defaultProps: {
        // Let Mantine handle theme colors automatically
      },
    },
    Text: {
      defaultProps: {
        // Let Mantine handle theme colors automatically
      },
    },
    Paper: {
      defaultProps: {
        // Let Mantine handle theme colors automatically
      },
    },
    AppShell: {
      defaultProps: {
        // Ensure AppShell uses theme colors
      },
    },
    Modal: {
      defaultProps: {
        // Ensure modals use theme colors
      },
    },
  },
  colors: {
    // Define custom colors for dark theme
    dark: [
      '#C1C2C5', // 0 (used for dark text)
      '#A6A7AB', // 1
      '#909296', // 2
      '#5C5F66', // 3
      '#373A40', // 4
      '#2C2E33', // 5
      '#25262B', // 6
      '#1A1B1E', // 7 (used for dark page background)
      '#141517', // 8
      '#101113', // 9
    ],
  },
  other: {
    // Custom theme values
    darkBg: '#1A1B1E',
    darkCard: '#25262B',
    darkText: '#C1C2C5',
    darkBorder: '#373A40',
  },
};

// Create the theme using the explicitly typed override object
const theme = createTheme(themeOverride);

export function MantineProvider({ children }: { children: ReactNode }) {
  return (
    <Provider
      theme={theme}
      defaultColorScheme="dark"
      cssVariablesSelector="html"
    >
      <ModalsProvider>
        <Notifications position="bottom-right" limit={5} zIndex={1000} />
        {children}
      </ModalsProvider>
    </Provider>
  );
}