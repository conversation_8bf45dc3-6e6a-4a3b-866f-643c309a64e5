import { getSupabaseClient } from './supabase';

export interface AuditLogEntry {
  user_id?: string | null;
  user_email: string;
  company_id?: string | null;
  action: string;
  resource_type: string;
  resource_id?: string | null;
  old_values?: any;
  new_values?: any;
  ip_address?: string | null;
  user_agent?: string | null;
  metadata?: any;
}

/**
 * Create an audit log entry
 */
export async function createAuditLog(entry: AuditLogEntry): Promise<void> {
  try {
    const supabase = getSupabaseClient();
    
    const { error } = await supabase
      .from('audit_logs')
      .insert({
        user_id: entry.user_id,
        user_email: entry.user_email,
        company_id: entry.company_id,
        action: entry.action,
        resource_type: entry.resource_type,
        resource_id: entry.resource_id,
        old_values: entry.old_values,
        new_values: entry.new_values,
        ip_address: entry.ip_address,
        user_agent: entry.user_agent,
        metadata: entry.metadata || {}
      });

    if (error) {
      console.error('Failed to create audit log:', error);
      // Don't throw error to avoid breaking the main operation
    }
  } catch (error) {
    console.error('Audit log creation error:', error);
    // Don't throw error to avoid breaking the main operation
  }
}

/**
 * Get audit logs with filtering and pagination
 */
export async function getAuditLogs(options: {
  page?: number;
  limit?: number;
  user_id?: string;
  company_id?: string;
  action?: string;
  resource_type?: string;
  resource_id?: string;
  start_date?: string;
  end_date?: string;
}) {
  try {
    const supabase = getSupabaseClient();
    const {
      page = 1,
      limit = 50,
      user_id,
      company_id,
      action,
      resource_type,
      resource_id,
      start_date,
      end_date
    } = options;

    const offset = (page - 1) * limit;

    let query = supabase
      .from('audit_logs')
      .select(`
        id,
        created_at,
        user_id,
        user_email,
        company_id,
        action,
        resource_type,
        resource_id,
        old_values,
        new_values,
        ip_address,
        user_agent,
        metadata
      `)
      .order('created_at', { ascending: false });

    // Apply filters
    if (user_id) {
      query = query.eq('user_id', user_id);
    }

    if (company_id) {
      query = query.eq('company_id', company_id);
    }

    if (action) {
      query = query.eq('action', action);
    }

    if (resource_type) {
      query = query.eq('resource_type', resource_type);
    }

    if (resource_id) {
      query = query.eq('resource_id', resource_id);
    }

    if (start_date) {
      query = query.gte('created_at', start_date);
    }

    if (end_date) {
      query = query.lte('created_at', end_date);
    }

    // Get total count
    const { count } = await supabase
      .from('audit_logs')
      .select('*', { count: 'exact', head: true });

    // Get paginated results
    const { data: auditLogs, error } = await query
      .range(offset, offset + limit - 1);

    if (error) {
      throw error;
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return {
      data: auditLogs,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages
      }
    };

  } catch (error) {
    console.error('Error fetching audit logs:', error);
    throw error;
  }
}

/**
 * Common audit actions
 */
export const AUDIT_ACTIONS = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  ACTIVATE: 'ACTIVATE',
  DEACTIVATE: 'DEACTIVATE',
  EXPORT: 'EXPORT',
  IMPORT: 'IMPORT',
  VIEW: 'VIEW'
} as const;

/**
 * Common resource types
 */
export const RESOURCE_TYPES = {
  CORE_FIELD: 'core_field',
  CUSTOM_FIELD: 'custom_field',
  RECORD: 'record',
  COMPANY: 'company',
  PROFILE: 'profile',
  SETTINGS: 'settings'
} as const;

/**
 * Helper function to create audit log for field changes
 */
export async function auditFieldChange(
  action: string,
  resourceType: string,
  resourceId: string,
  oldValues: any,
  newValues: any,
  userInfo: {
    user_id?: string;
    user_email: string;
    company_id?: string;
  },
  requestInfo?: {
    ip_address?: string;
    user_agent?: string;
  }
) {
  await createAuditLog({
    user_id: userInfo.user_id,
    user_email: userInfo.user_email,
    company_id: userInfo.company_id,
    action,
    resource_type: resourceType,
    resource_id: resourceId,
    old_values: oldValues,
    new_values: newValues,
    ip_address: requestInfo?.ip_address,
    user_agent: requestInfo?.user_agent
  });
}

/**
 * Helper function to create audit log for user actions
 */
export async function auditUserAction(
  action: string,
  resourceType: string,
  userInfo: {
    user_id?: string;
    user_email: string;
    company_id?: string;
  },
  metadata?: any,
  requestInfo?: {
    ip_address?: string;
    user_agent?: string;
  }
) {
  await createAuditLog({
    user_id: userInfo.user_id,
    user_email: userInfo.user_email,
    company_id: userInfo.company_id,
    action,
    resource_type: resourceType,
    metadata,
    ip_address: requestInfo?.ip_address,
    user_agent: requestInfo?.user_agent
  });
}
