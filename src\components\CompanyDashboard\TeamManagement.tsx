'use client';

import { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  Text,
  Group,
  Button,
  Table,
  Badge,
  ActionIcon,
  Menu,
  Paper,
  Alert,
  LoadingOverlay,
  Modal,
  TextInput,
  Select,
  Textarea,
  Switch,
  Avatar,
  Tabs,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconPlus,
  IconDots,
  IconEdit,
  IconTrash,
  IconMail,
  IconUsers,
  IconUserCheck,
  IconUserX,
  IconAlertCircle,
  IconCheck,
  IconCrown,
} from '@tabler/icons-react';
import { CompanyInvitation, InvitationFormData } from 'src/types/invitations';

interface TeamMember {
  id: string;
  full_name: string | null;
  email: string | null;
  role: string;
  is_company_admin: boolean;
  disabled: boolean;
  last_login_at: string | null;
  created_at: string;
}

export function TeamManagement() {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [invitations, setInvitations] = useState<CompanyInvitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [inviteModalOpen, setInviteModalOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('members');

  const inviteForm = useForm<InvitationFormData>({
    initialValues: {
      email: '',
      role: 'user',
      is_company_admin: false,
      message: '',
    },
    validate: {
      email: (value) => {
        if (!value) return 'Email is required';
        if (!/^\S+@\S+\.\S+$/.test(value)) return 'Invalid email format';
        return null;
      },
      role: (value) => (!value ? 'Role is required' : null),
    },
  });

  const loadTeamData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load team members and invitations in parallel
      const [membersResponse, invitationsResponse] = await Promise.all([
        fetch('/api/company/team'),
        fetch('/api/company/invitations')
      ]);

      if (!membersResponse.ok) {
        const errorData = await membersResponse.json();
        throw new Error(errorData.error || 'Failed to load team members');
      }

      const membersData = await membersResponse.json();
      setTeamMembers(membersData.data || []);

      if (invitationsResponse.ok) {
        const invitationsData = await invitationsResponse.json();
        setInvitations(invitationsData.data || []);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load team data';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTeamData();
  }, []);

  const handleInviteSubmit = async (values: InvitationFormData) => {
    try {
      setSubmitting(true);

      const response = await fetch('/api/company/invitations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send invitation');
      }

      notifications.show({
        title: 'Success',
        message: `Invitation sent to ${values.email}`,
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      inviteForm.reset();
      setInviteModalOpen(false);
      loadTeamData();

    } catch (error) {
      console.error('Error sending invitation:', error);
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to send invitation',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleToggleMemberStatus = async (member: TeamMember) => {
    try {
      const response = await fetch(`/api/company/team/${member.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          disabled: !member.disabled,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update member status');
      }

      notifications.show({
        title: 'Success',
        message: `Member ${member.disabled ? 'enabled' : 'disabled'} successfully`,
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      loadTeamData();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to update member status',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    }
  };

  const handleCancelInvitation = async (invitation: CompanyInvitation) => {
    try {
      const response = await fetch(`/api/company/invitations/${invitation.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'cancel',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to cancel invitation');
      }

      notifications.show({
        title: 'Success',
        message: 'Invitation cancelled successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      loadTeamData();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to cancel invitation',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    }
  };

  const handleResendInvitation = async (invitation: CompanyInvitation) => {
    try {
      const response = await fetch(`/api/company/invitations/${invitation.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'resend',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to resend invitation');
      }

      notifications.show({
        title: 'Success',
        message: 'Invitation resent successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      loadTeamData();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to resend invitation',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    }
  };

  if (loading) {
    return (
      <div style={{ position: 'relative', minHeight: 400 }}>
        <LoadingOverlay visible />
      </div>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
        {error}
      </Alert>
    );
  }

  return (
    <Stack gap="md">
      {/* Header */}
      <Group justify="space-between">
        <div>
          <Title order={3}>Team Management</Title>
          <Text c="dimmed">Manage your company team members and invitations</Text>
        </div>
        <Button
          leftSection={<IconPlus size={16} />}
          onClick={() => setInviteModalOpen(true)}
        >
          Invite Member
        </Button>
      </Group>

      {/* Stats */}
      <Group>
        <Paper withBorder p="md" style={{ flex: 1 }}>
          <Group>
            <IconUsers size={24} />
            <div>
              <Text size="sm" c="dimmed">Team Members</Text>
              <Text size="xl" fw={700}>{teamMembers.length}</Text>
            </div>
          </Group>
        </Paper>
        <Paper withBorder p="md" style={{ flex: 1 }}>
          <Group>
            <IconMail size={24} />
            <div>
              <Text size="sm" c="dimmed">Pending Invitations</Text>
              <Text size="xl" fw={700}>{invitations.filter(i => i.status === 'pending').length}</Text>
            </div>
          </Group>
        </Paper>
      </Group>

      {/* Tabs */}
      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value="members" leftSection={<IconUsers size={16} />}>
            Team Members ({teamMembers.length})
          </Tabs.Tab>
          <Tabs.Tab value="invitations" leftSection={<IconMail size={16} />}>
            Invitations ({invitations.length})
          </Tabs.Tab>
        </Tabs.List>

        {/* Team Members Tab */}
        <Tabs.Panel value="members" pt="md">
          <Paper withBorder>
            {teamMembers.length === 0 ? (
              <Stack align="center" p="xl">
                <Text c="dimmed">No team members yet</Text>
                <Button
                  leftSection={<IconPlus size={16} />}
                  onClick={() => setInviteModalOpen(true)}
                >
                  Invite Your First Team Member
                </Button>
              </Stack>
            ) : (
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Member</Table.Th>
                    <Table.Th>Role</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Last Login</Table.Th>
                    <Table.Th>Joined</Table.Th>
                    <Table.Th width={100}>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {teamMembers.map((member) => (
                    <Table.Tr key={member.id}>
                      <Table.Td>
                        <Group>
                          <Avatar size="sm" radius="xl">
                            {member.full_name?.charAt(0) || member.email?.charAt(0)}
                          </Avatar>
                          <div>
                            <Text fw={500}>{member.full_name || 'No name'}</Text>
                            <Text size="sm" c="dimmed">{member.email}</Text>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <Badge variant="light">{member.role}</Badge>
                          {member.is_company_admin && (
                            <Badge color="red" size="sm" leftSection={<IconCrown size={12} />}>
                              Admin
                            </Badge>
                          )}
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Badge
                          color={member.disabled ? 'red' : 'green'}
                          variant="light"
                        >
                          {member.disabled ? 'Disabled' : 'Active'}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">
                          {member.last_login_at 
                            ? new Date(member.last_login_at).toLocaleDateString()
                            : 'Never'
                          }
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">
                          {new Date(member.created_at).toLocaleDateString()}
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Menu shadow="md" width={200}>
                          <Menu.Target>
                            <ActionIcon variant="subtle">
                              <IconDots size={16} />
                            </ActionIcon>
                          </Menu.Target>
                          <Menu.Dropdown>
                            <Menu.Item
                              leftSection={<IconEdit size={14} />}
                            >
                              Edit Role
                            </Menu.Item>
                            <Menu.Item
                              leftSection={member.disabled ? <IconUserCheck size={14} /> : <IconUserX size={14} />}
                              onClick={() => handleToggleMemberStatus(member)}
                            >
                              {member.disabled ? 'Enable' : 'Disable'}
                            </Menu.Item>
                          </Menu.Dropdown>
                        </Menu>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            )}
          </Paper>
        </Tabs.Panel>

        {/* Invitations Tab */}
        <Tabs.Panel value="invitations" pt="md">
          <Paper withBorder>
            {invitations.length === 0 ? (
              <Stack align="center" p="xl">
                <Text c="dimmed">No pending invitations</Text>
              </Stack>
            ) : (
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Email</Table.Th>
                    <Table.Th>Role</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Sent</Table.Th>
                    <Table.Th>Expires</Table.Th>
                    <Table.Th width={100}>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {invitations.map((invitation) => (
                    <Table.Tr key={invitation.id}>
                      <Table.Td>
                        <Text fw={500}>{invitation.email}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <Badge variant="light">{invitation.role}</Badge>
                          {invitation.is_company_admin && (
                            <Badge color="red" size="sm">Admin</Badge>
                          )}
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Badge
                          color={
                            invitation.status === 'pending' ? 'yellow' :
                            invitation.status === 'accepted' ? 'green' :
                            invitation.status === 'declined' ? 'red' : 'gray'
                          }
                          variant="light"
                        >
                          {invitation.status}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">
                          {new Date(invitation.created_at).toLocaleDateString()}
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">
                          {new Date(invitation.expires_at).toLocaleDateString()}
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        {invitation.status === 'pending' && (
                          <Menu shadow="md" width={200}>
                            <Menu.Target>
                              <ActionIcon variant="subtle">
                                <IconDots size={16} />
                              </ActionIcon>
                            </Menu.Target>
                            <Menu.Dropdown>
                              <Menu.Item
                                leftSection={<IconMail size={14} />}
                                onClick={() => handleResendInvitation(invitation)}
                              >
                                Resend
                              </Menu.Item>
                              <Menu.Item
                                leftSection={<IconTrash size={14} />}
                                color="red"
                                onClick={() => handleCancelInvitation(invitation)}
                              >
                                Cancel
                              </Menu.Item>
                            </Menu.Dropdown>
                          </Menu>
                        )}
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            )}
          </Paper>
        </Tabs.Panel>
      </Tabs>

      {/* Invite Member Modal */}
      <Modal
        opened={inviteModalOpen}
        onClose={() => setInviteModalOpen(false)}
        title="Invite Team Member"
        size="md"
        closeOnClickOutside={!submitting}
        closeOnEscape={!submitting}
      >
        <div style={{ position: 'relative' }}>
          <LoadingOverlay visible={submitting} />
          
          <form onSubmit={inviteForm.onSubmit(handleInviteSubmit)}>
            <Stack gap="md">
              <TextInput
                label="Email Address"
                placeholder="Enter email address"
                required
                {...inviteForm.getInputProps('email')}
              />

              <Select
                label="Role"
                data={[
                  { value: 'user', label: 'Regular User' },
                  { value: 'admin', label: 'Admin' },
                  { value: 'viewer', label: 'Viewer' },
                ]}
                required
                {...inviteForm.getInputProps('role')}
              />

              <Switch
                label="Company Administrator"
                description="Give this user company admin privileges"
                {...inviteForm.getInputProps('is_company_admin', { type: 'checkbox' })}
              />

              <Textarea
                label="Personal Message (Optional)"
                placeholder="Add a personal message to the invitation"
                rows={3}
                {...inviteForm.getInputProps('message')}
              />

              <Group justify="flex-end" mt="md">
                <Button
                  variant="outline"
                  onClick={() => setInviteModalOpen(false)}
                  disabled={submitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  loading={submitting}
                  leftSection={<IconMail size={16} />}
                >
                  Send Invitation
                </Button>
              </Group>
            </Stack>
          </form>
        </div>
      </Modal>
    </Stack>
  );
}
