import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { verifyAdminAuth } from 'src/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    const supabase = getSupabaseClient();

    // Get profiles with contact count
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select(`
        id,
        created_at,
        updated_at,
        full_name,
        email,
        avatar_url,
        disabled
      `)
      .order('updated_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
      return NextResponse.json({ error: 'Failed to fetch profiles' }, { status: 500 });
    }

    // Simplified profiles without contact counts
    const finalProfiles = profiles || [];

    // Calculate total count based on access level
    let totalCount = 0;
    if (authResult.isSuperAdmin) {
      const { count, error: countError } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      if (countError) {
        console.error('Error fetching profiles count:', countError);
        return NextResponse.json({ error: 'Failed to fetch profiles count' }, { status: 500 });
      }
      totalCount = count || 0;
    } else {
      // For owners, use the filtered profiles count
      totalCount = finalProfiles.length;
    }

    return NextResponse.json({
      data: finalProfiles,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });

  } catch (error) {
    console.error('Admin profiles API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can delete profiles
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - only super admin can delete profiles' }, { status: 403 });
    }

    const { profileEmail } = await request.json();

    if (!profileEmail) {
      return NextResponse.json({ error: 'Profile email is required' }, { status: 400 });
    }

    const supabase = getSupabaseClient();

    // Delete user data in order
    // 1. Delete user settings
    const { error: settingsError } = await supabase
      .from('settings')
      .delete()
      .eq('email', profileEmail);

    if (settingsError) {
      console.error('Error deleting settings:', settingsError);
    }

    // 2. Finally, delete the profile
    const { error: profileError } = await supabase
      .from('profiles')
      .delete()
      .eq('email', profileEmail);

    if (profileError) {
      console.error('Error deleting profile:', profileError);
      return NextResponse.json({ error: 'Failed to delete profile' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Profile deleted successfully' });

  } catch (error) {
    console.error('Admin delete profile API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
