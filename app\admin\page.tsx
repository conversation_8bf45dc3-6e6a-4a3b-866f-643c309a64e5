'use client';

import { useEffect, useState } from 'react';
import {
  Title,
  Text,
  Alert,
  LoadingOverlay,
  SimpleGrid,
  Paper,
  Group,
  ThemeIcon,
  Stack,
  Tabs,
  Container,
} from '@mantine/core';
import {
  IconAlertCircle,
  IconUsers,
  IconShield,
  IconChartBar,
  IconBuilding,
  IconDatabase,
  IconFileText,
} from '@tabler/icons-react';
import { AdminStats, fetchAdminStats } from 'src/lib/admin';

import { AdminLayout } from 'src/components/layouts/AdminLayout';
import { checkAdminAccess, AdminAccessInfo } from 'src/lib/adminClient';
import { CoreFieldsManagement } from 'src/components/Admin/CoreFields/CoreFieldsManagement';
import { AnalyticsDashboard } from 'src/components/Admin/Analytics/AnalyticsDashboard';
import { EnhancedProfileManagement } from 'src/components/Admin/Profiles/EnhancedProfileManagement';
import { AuditLogsViewer } from 'src/components/Admin/AuditLogs/AuditLogsViewer';
import { CompanyManagement } from 'src/components/Admin/Companies/CompanyManagement';

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
}

function StatCard({ title, value, icon, color }: StatCardProps) {
  return (
    <Paper withBorder p="md" radius="md">
      <Group justify="apart">
        <div>
          <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
            {title}
          </Text>
          <Text fw={700} fz="xl">
            {value.toLocaleString()}
          </Text>
        </div>
        <ThemeIcon color={color} variant="light" size={38} radius="md">
          {icon}
        </ThemeIcon>
      </Group>
    </Paper>
  );
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [adminAccess, setAdminAccess] = useState<AdminAccessInfo | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');

  useEffect(() => {
    // Check admin access and fetch stats
    const fetchData = async () => {
      try {
        // First check admin access
        const accessInfo = await checkAdminAccess();
        setAdminAccess(accessInfo);

        if (!accessInfo.isAuthorized) {
          setError(accessInfo.error || 'Unauthorized access');
          return;
        }

        // Then fetch stats
        const statsData = await fetchAdminStats();
        setStats(statsData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <AdminLayout>
        <LoadingOverlay visible />
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
      </AdminLayout>
    );
  }

  // Only allow super admin access to /admin
  if (adminAccess && !adminAccess.isSuperAdmin) {
    return (
      <AdminLayout>
        <Alert icon={<IconAlertCircle size={16} />} title="Access Denied" color="red">
          Super admin privileges required. Use /owner for namespace management.
        </Alert>
      </AdminLayout>
    );
  }

  // Show super admin dashboard
  return (
    <AdminLayout>
      <Container size="xl" px={0}>
        <Stack gap="xl">
          {/* Header */}
          <Group justify="space-between">
            <Group>
              <ThemeIcon size={40} radius="md" color="red">
                <IconShield size={24} />
              </ThemeIcon>
              <div>
                <Title order={1}>Admin Dashboard</Title>
                <Text c="dimmed">Manage companies, fields, users, and system analytics</Text>
              </div>
            </Group>
          </Group>

          {/* Tabbed Interface */}
          <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'overview')}>
            <Tabs.List>
              <Tabs.Tab value="overview" leftSection={<IconShield size={16} />}>
                Overview
              </Tabs.Tab>
              <Tabs.Tab value="companies" leftSection={<IconBuilding size={16} />}>
                Companies
              </Tabs.Tab>
              <Tabs.Tab value="core-fields" leftSection={<IconDatabase size={16} />}>
                Core Fields
              </Tabs.Tab>
              <Tabs.Tab value="profiles" leftSection={<IconUsers size={16} />}>
                Profiles
              </Tabs.Tab>
              <Tabs.Tab value="analytics" leftSection={<IconChartBar size={16} />}>
                Analytics
              </Tabs.Tab>
              <Tabs.Tab value="audit-logs" leftSection={<IconFileText size={16} />}>
                Audit Logs
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="overview" pt="md">
              <Stack gap="md">
                {/* Statistics Cards */}
                {stats && (
                  <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="md">
                    <StatCard
                      title="Profiles"
                      value={stats.profiles}
                      icon={<IconUsers size={18} />}
                      color="blue"
                    />
                  </SimpleGrid>
                )}

                {/* Quick Actions or Recent Activity could go here */}
                <Paper p="md" withBorder>
                  <Title order={3} mb="md">System Overview</Title>
                  <Text c="dimmed">
                    Welcome to the ODude CRM Admin Dashboard. Use the tabs above to manage different aspects of the system:
                  </Text>
                  <Stack gap="xs" mt="md">
                    <Text size="sm">• <strong>Companies:</strong> Manage tenant companies and their subscriptions</Text>
                    <Text size="sm">• <strong>Core Fields:</strong> Define fields that apply to all companies</Text>
                    <Text size="sm">• <strong>Profiles:</strong> Manage user accounts and permissions</Text>
                    <Text size="sm">• <strong>Analytics:</strong> View system-wide reports and metrics</Text>
                  </Stack>
                </Paper>
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="companies" pt="md">
              <CompanyManagement />
            </Tabs.Panel>

            <Tabs.Panel value="core-fields" pt="md">
              <CoreFieldsManagement />
            </Tabs.Panel>

            <Tabs.Panel value="profiles" pt="md">
              <EnhancedProfileManagement />
            </Tabs.Panel>

            <Tabs.Panel value="analytics" pt="md">
              <AnalyticsDashboard />
            </Tabs.Panel>

            <Tabs.Panel value="audit-logs" pt="md">
              <AuditLogsViewer />
            </Tabs.Panel>
          </Tabs>
        </Stack>
      </Container>
    </AdminLayout>
  );
}
