import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';
import { createAuditLog } from 'src/lib/auditLog';

/**
 * GET /api/company/custom-fields/[id]
 * Get a specific custom field
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;
    const { id } = params;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company' }, { status: 403 });
    }

    // Get the custom field
    const { data: customField, error } = await supabase
      .from('custom_fields')
      .select(`
        id,
        created_at,
        updated_at,
        name,
        field_key,
        field_type,
        description,
        is_required,
        is_active,
        default_value,
        validation_rules,
        dropdown_options,
        display_order,
        created_by,
        updated_by
      `)
      .eq('id', id)
      .eq('company_id', profile.company_id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Custom field not found' }, { status: 404 });
      }
      console.error('Error fetching custom field:', error);
      return NextResponse.json({ error: 'Failed to fetch custom field' }, { status: 500 });
    }

    return NextResponse.json({ data: customField });

  } catch (error) {
    console.error('Custom field GET API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PUT /api/company/custom-fields/[id]
 * Update a custom field
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;
    const { id } = params;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, is_company_admin, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company' }, { status: 403 });
    }

    if (!profile.is_company_admin) {
      return NextResponse.json({ error: 'Company admin access required' }, { status: 403 });
    }

    // Get existing custom field
    const { data: existingField, error: fetchError } = await supabase
      .from('custom_fields')
      .select('*')
      .eq('id', id)
      .eq('company_id', profile.company_id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Custom field not found' }, { status: 404 });
      }
      console.error('Error fetching existing custom field:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch custom field' }, { status: 500 });
    }

    const body = await request.json();
    const {
      name,
      field_type,
      description,
      is_required,
      is_active,
      default_value,
      validation_rules,
      dropdown_options,
      display_order
    } = body;

    // Prepare update data
    const updateData: any = {
      updated_by: profile.id,
      updated_at: new Date().toISOString()
    };

    if (name !== undefined) {
      if (!name) {
        return NextResponse.json({ error: 'Name is required' }, { status: 400 });
      }
      updateData.name = name;
    }

    if (field_type !== undefined) {
      const validFieldTypes = ['text', 'number', 'dropdown', 'checkbox', 'date', 'email', 'phone', 'price'];
      if (!validFieldTypes.includes(field_type)) {
        return NextResponse.json({ error: 'Invalid field type' }, { status: 400 });
      }
      updateData.field_type = field_type;
    }

    if (description !== undefined) {
      updateData.description = description || null;
    }

    if (typeof is_required === 'boolean') {
      updateData.is_required = is_required;
    }

    if (typeof is_active === 'boolean') {
      updateData.is_active = is_active;
    }

    if (default_value !== undefined) {
      updateData.default_value = default_value || null;
    }

    if (validation_rules !== undefined) {
      updateData.validation_rules = validation_rules || {};
    }

    if (dropdown_options !== undefined) {
      updateData.dropdown_options = dropdown_options;
    }

    if (typeof display_order === 'number') {
      updateData.display_order = display_order;
    }

    // Update the custom field
    const { data: updatedField, error: updateError } = await supabase
      .from('custom_fields')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating custom field:', updateError);
      return NextResponse.json({ error: 'Failed to update custom field' }, { status: 500 });
    }

    // Create audit log
    await createAuditLog({
      user_id: profile.id,
      user_email: userEmail,
      company_id: profile.company_id,
      action: 'UPDATE',
      resource_type: 'custom_field',
      resource_id: id,
      old_values: existingField,
      new_values: updatedField,
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      user_agent: request.headers.get('user-agent')
    });

    return NextResponse.json({
      data: updatedField,
      message: 'Custom field updated successfully'
    });

  } catch (error) {
    console.error('Custom field PUT API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE /api/company/custom-fields/[id]
 * Delete a custom field
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;
    const { id } = params;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, is_company_admin, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company' }, { status: 403 });
    }

    if (!profile.is_company_admin) {
      return NextResponse.json({ error: 'Company admin access required' }, { status: 403 });
    }

    // Get existing custom field for audit log
    const { data: existingField, error: fetchError } = await supabase
      .from('custom_fields')
      .select('*')
      .eq('id', id)
      .eq('company_id', profile.company_id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Custom field not found' }, { status: 404 });
      }
      console.error('Error fetching existing custom field:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch custom field' }, { status: 500 });
    }

    // Delete the custom field
    const { error: deleteError } = await supabase
      .from('custom_fields')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting custom field:', deleteError);
      return NextResponse.json({ error: 'Failed to delete custom field' }, { status: 500 });
    }

    // Create audit log
    await createAuditLog({
      user_id: profile.id,
      user_email: userEmail,
      company_id: profile.company_id,
      action: 'DELETE',
      resource_type: 'custom_field',
      resource_id: id,
      old_values: existingField,
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      user_agent: request.headers.get('user-agent')
    });

    return NextResponse.json({ message: 'Custom field deleted successfully' });

  } catch (error) {
    console.error('Custom field DELETE API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
