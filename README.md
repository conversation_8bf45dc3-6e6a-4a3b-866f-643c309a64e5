# ODude CRM Boilerplate

A clean, modern CRM boilerplate built with Next.js 15, Supabase, and Mantine UI. This boilerplate provides a solid foundation for building CRM applications with user authentication, profile management, and admin functionality.

## ✨ Features

- 🔐 **Authentication**: Complete auth system with NextAuth.js (Google OAuth)
- 👤 **User Profiles**: User profile management with Supabase
- ⚙️ **Settings**: User-specific settings and configurations
- 🛡️ **Admin Dashboard**: Super admin panel with system monitoring
- 📱 **Responsive Design**: Mobile-first design with Mantine UI
- 🎨 **Dark/Light Mode**: Built-in theme switching
- 🔒 **Type Safety**: Full TypeScript support
- 📊 **Database**: PostgreSQL with Supabase
- 🚀 **Performance**: Optimized Next.js 15 with App Router

## 🏗️ Architecture

This boilerplate uses a clean architecture with:
- **Database**: Only `profiles` and `settings` tables (minimal setup)
- **Authentication**: NextAuth.js with Google provider
- **Admin System**: Super admin access with passkey verification
- **UI Components**: Modular Mantine UI components
- **API Routes**: RESTful API with proper error handling

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm/yarn/pnpm
- Supabase account
- Google OAuth credentials (optional)

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd odude-crm
```

2. **Install dependencies:**
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. **Environment Setup:**
Create `.env.local` file:

```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-super-secret-key-here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Google OAuth (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# SMTP Configuration (for report forms)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
CONTACT_EMAIL=<EMAIL>

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
```

4. **Database Setup:**
   - Go to your Supabase project dashboard
   - Navigate to SQL Editor
   - Run the script from `scripts/fresh-database-setup.sql`

5. **Start Development Server:**
```bash
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see your application.

## 📁 Project Structure

```
├── app/                          # Next.js App Router
│   ├── admin/                   # Admin dashboard
│   │   └── page.tsx            # Admin main page
│   ├── api/                    # API routes
│   │   ├── admin/              # Admin API endpoints
│   │   ├── auth/               # NextAuth endpoints
│   │   └── report/             # Report form endpoint
│   ├── auth/                   # Authentication pages
│   ├── privacy-policy/         # Privacy policy page
│   ├── report/                 # Report form page
│   ├── globals.css             # Global styles
│   ├── layout.tsx              # Root layout
│   └── page.tsx                # Home page
├── src/
│   ├── components/             # React components
│   │   ├── Admin/              # Admin-specific components
│   │   ├── Buttons/            # Button components
│   │   ├── Footer/             # Footer component
│   │   ├── SignIn/             # Sign-in component
│   │   ├── SignedIn/           # Signed-in user component
│   │   └── layouts/            # Layout components
│   ├── lib/                    # Utilities and configurations
│   │   ├── admin.ts            # Admin utilities
│   │   ├── adminAuth.ts        # Admin authentication
│   │   ├── adminClient.ts      # Admin client functions
│   │   ├── common.ts           # Common utilities
│   │   ├── config.ts           # App configuration
│   │   ├── database.types.ts   # Database type definitions
│   │   └── supabase.ts         # Supabase client
│   └── hooks/                  # Custom React hooks
├── scripts/
│   └── fresh-database-setup.sql # Database initialization
├── public/                     # Static assets
└── middleware.ts               # Next.js middleware
```

## 🗄️ Database Schema

The boilerplate uses a minimal database schema with just two tables:

### Profiles Table
```sql
CREATE TABLE profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  full_name TEXT,
  avatar_url TEXT,
  email TEXT UNIQUE,
  disabled BOOLEAN DEFAULT FALSE
);
```

### Settings Table
```sql
CREATE TABLE settings (
  email TEXT PRIMARY KEY,
  max_contact_limit INTEGER DEFAULT 4,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🔧 Configuration

### Admin Access
- Set your admin email in `.env.local` as `ADMIN_EMAIL`
- Admin users can access `/admin` dashboard
- Admin passkey verification for secure actions

### Authentication Providers
Currently supports:
- Google OAuth (configured)
- Easy to extend for other providers

### Customization
- Update `src/lib/config.ts` for app-wide settings
- Modify `app/globals.css` for global styles
- Customize Mantine theme in `app/layout.tsx`

## 📚 API Routes

### Public Routes
- `POST /api/auth/[...nextauth]` - NextAuth endpoints
- `POST /api/report` - Report form submission

### Admin Routes (Protected)
- `GET /api/admin/access-check` - Check admin access
- `GET /api/admin/stats` - Get system statistics
- `GET /api/admin/profiles` - Get user profiles
- `DELETE /api/admin/profiles` - Delete user profile
- `PATCH /api/admin/profiles/toggle-disable` - Toggle profile status
- `GET /api/admin/system-info` - System health check
- `POST /api/admin/verify-passkey` - Verify admin passkey

## 🎨 UI Components

### Core Components
- **SignIn**: Authentication interface
- **SignedIn**: Dashboard for authenticated users
- **AdminLayout**: Layout wrapper for admin pages
- **UserMenu**: User dropdown menu
- **NavBar**: Navigation component

### Admin Components
- **AdminDashboard**: Main admin interface
- **AdminPasskeyModal**: Secure admin verification
- **SystemInfo**: System health monitoring

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms
The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🔒 Security Features

- **Environment Variables**: Sensitive data in env files
- **Admin Authentication**: Multi-layer admin protection
- **CSRF Protection**: Built-in NextAuth CSRF protection
- **Type Safety**: TypeScript prevents runtime errors
- **Input Validation**: Server-side validation for all inputs

## 🧪 Development

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks
```

### Adding New Features
1. **Database**: Add tables to `scripts/fresh-database-setup.sql`
2. **Types**: Update `src/lib/database.types.ts`
3. **API**: Create routes in `app/api/`
4. **Components**: Add to `src/components/`
5. **Pages**: Add to `app/`

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

If you have any questions or need help with setup, please:
1. Check the documentation above
2. Look through existing issues
3. Create a new issue with detailed information

---

**Built with ❤️ using Next.js, Supabase, and Mantine UI**
