import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { verifyAdminAuth } from 'src/lib/adminAuth';
import { createAuditLog } from 'src/lib/auditLog';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can manage companies
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';
    const subscriptionPlan = searchParams.get('subscriptionPlan') || '';
    const subscriptionStatus = searchParams.get('subscriptionStatus') || '';
    const offset = (page - 1) * limit;

    const supabase = getSupabaseClient();

    // Build query
    let query = supabase
      .from('companies')
      .select(`
        id,
        created_at,
        updated_at,
        name,
        slug,
        domain,
        logo_url,
        description,
        subscription_plan,
        subscription_status,
        max_users,
        max_records,
        settings,
        disabled
      `)
      .order('created_at', { ascending: false });

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,slug.ilike.%${search}%,domain.ilike.%${search}%`);
    }

    if (subscriptionPlan) {
      query = query.eq('subscription_plan', subscriptionPlan);
    }

    if (subscriptionStatus) {
      query = query.eq('subscription_status', subscriptionStatus);
    }

    // Get total count for pagination
    const { count } = await supabase
      .from('companies')
      .select('*', { count: 'exact', head: true });

    // Get paginated results
    const { data: companies, error: companiesError } = await query
      .range(offset, offset + limit - 1);

    if (companiesError) {
      console.error('Error fetching companies:', companiesError);
      return NextResponse.json({ error: 'Failed to fetch companies' }, { status: 500 });
    }

    // Get user counts for each company
    const companyIds = companies.map(company => company.id);
    let userCounts: { [key: string]: number } = {};
    
    if (companyIds.length > 0) {
      const { data: userCountsData } = await supabase
        .from('profiles')
        .select('company_id')
        .in('company_id', companyIds)
        .not('company_id', 'is', null);

      if (userCountsData) {
        userCounts = userCountsData.reduce((acc, profile) => {
          acc[profile.company_id] = (acc[profile.company_id] || 0) + 1;
          return acc;
        }, {} as { [key: string]: number });
      }
    }

    // Get record counts for each company
    let recordCounts: { [key: string]: number } = {};
    
    if (companyIds.length > 0) {
      const { data: recordCountsData } = await supabase
        .from('records')
        .select('company_id')
        .in('company_id', companyIds)
        .eq('status', 'active');

      if (recordCountsData) {
        recordCounts = recordCountsData.reduce((acc, record) => {
          acc[record.company_id] = (acc[record.company_id] || 0) + 1;
          return acc;
        }, {} as { [key: string]: number });
      }
    }

    // Enrich companies with counts
    const enrichedCompanies = companies.map(company => ({
      ...company,
      user_count: userCounts[company.id] || 0,
      record_count: recordCounts[company.id] || 0
    }));

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: enrichedCompanies,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages
      }
    });

  } catch (error) {
    console.error('Companies API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can create companies
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const {
      name,
      slug,
      domain,
      logo_url,
      description,
      subscription_plan = 'free',
      subscription_status = 'active',
      max_users = 10,
      max_records = 1000,
      settings = {}
    } = body;

    // Validation
    if (!name || !slug) {
      return NextResponse.json({ 
        error: 'Missing required fields: name, slug' 
      }, { status: 400 });
    }

    // Validate slug format (alphanumeric and hyphens only)
    if (!/^[a-z0-9-]+$/.test(slug)) {
      return NextResponse.json({ 
        error: 'Slug must contain only lowercase letters, numbers, and hyphens' 
      }, { status: 400 });
    }

    // Validate subscription plan
    const validPlans = ['free', 'basic', 'premium', 'enterprise'];
    if (!validPlans.includes(subscription_plan)) {
      return NextResponse.json({ 
        error: `Invalid subscription plan. Valid plans: ${validPlans.join(', ')}` 
      }, { status: 400 });
    }

    // Validate subscription status
    const validStatuses = ['active', 'suspended', 'cancelled'];
    if (!validStatuses.includes(subscription_status)) {
      return NextResponse.json({ 
        error: `Invalid subscription status. Valid statuses: ${validStatuses.join(', ')}` 
      }, { status: 400 });
    }

    const supabase = getSupabaseClient();
    const userId = authResult.session?.user?.id;

    // Check if slug already exists
    const { data: existingCompany } = await supabase
      .from('companies')
      .select('id')
      .eq('slug', slug)
      .single();

    if (existingCompany) {
      return NextResponse.json({ 
        error: 'Company slug already exists' 
      }, { status: 409 });
    }

    // Check if domain already exists (if provided)
    if (domain) {
      const { data: existingDomain } = await supabase
        .from('companies')
        .select('id')
        .eq('domain', domain)
        .single();

      if (existingDomain) {
        return NextResponse.json({ 
          error: 'Domain already exists' 
        }, { status: 409 });
      }
    }

    // Create the company
    const { data: newCompany, error: createError } = await supabase
      .from('companies')
      .insert({
        name,
        slug,
        domain,
        logo_url,
        description,
        subscription_plan,
        subscription_status,
        max_users,
        max_records,
        settings
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating company:', createError);
      return NextResponse.json({ error: 'Failed to create company' }, { status: 500 });
    }

    // Create audit log
    await createAuditLog({
      user_id: userId,
      user_email: authResult.session?.user?.email || '',
      action: 'CREATE',
      resource_type: 'company',
      resource_id: newCompany.id,
      new_values: newCompany,
      ip_address: request.ip,
      user_agent: request.headers.get('user-agent')
    });

    return NextResponse.json({ 
      data: newCompany,
      message: 'Company created successfully' 
    }, { status: 201 });

  } catch (error) {
    console.error('Companies POST API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
