'use client';

import { useState, useEffect } from 'react';
import {
  Container,
  Title,
  Text,
  Alert,
  LoadingOverlay,
  Paper,
  Stack,
} from '@mantine/core';
import {
  IconAlertCircle,
  IconCheck,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { DynamicFormBuilder } from 'src/components/DynamicForm/DynamicFormBuilder';
import { CoreField } from 'src/types/coreFields';

interface CustomField {
  id: string;
  name: string;
  field_key: string;
  field_type: string;
  description: string | null;
  is_required: boolean;
  is_active: boolean;
  default_value: string | null;
  validation_rules: Record<string, any>;
  dropdown_options: string[] | null;
  display_order: number;
}

interface UserProfile {
  id: string;
  company_id: string | null;
}

export default function FormPage() {
  const [coreFields, setCoreFields] = useState<CoreField[]>([]);
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load form fields
  const loadFields = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/forms/fields');
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load form fields');
      }

      const data = await response.json();
      setCoreFields(data.coreFields);
      setCustomFields(data.customFields);
      setUserProfile(data.userProfile);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load form fields';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFields();
  }, []);

  // Handle form submission
  const handleSubmit = async (data: { coreFieldData: Record<string, any>; customFieldData: Record<string, any> }) => {
    try {
      const response = await fetch('/api/records', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit form');
      }

      const result = await response.json();
      
      notifications.show({
        title: 'Success',
        message: 'Your information has been submitted successfully!',
        color: 'green',
        icon: <IconCheck size={16} />
      });

      // Optionally redirect or reset form
      // window.location.href = '/success';
    } catch (err) {
      throw err; // Re-throw to let DynamicFormBuilder handle the error display
    }
  };

  if (loading) {
    return (
      <Container size="md" py="xl">
        <div style={{ position: 'relative', minHeight: 400 }}>
          <LoadingOverlay visible />
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="md" py="xl">
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
      </Container>
    );
  }

  if (!userProfile?.company_id) {
    return (
      <Container size="md" py="xl">
        <Alert icon={<IconAlertCircle size={16} />} title="Access Restricted" color="yellow">
          You must be associated with a company to access this form. Please contact your administrator.
        </Alert>
      </Container>
    );
  }

  return (
    <Container size="md" py="xl">
      <Stack gap="xl">
        {/* Header */}
        <Paper p="md" withBorder>
          <Title order={1} ta="center" mb="md">
            Submit Your Information
          </Title>
          <Text ta="center" c="dimmed">
            Please fill out the form below with your information. All required fields must be completed.
          </Text>
        </Paper>

        {/* Dynamic Form */}
        <DynamicFormBuilder
          coreFields={coreFields}
          customFields={customFields}
          onSubmit={handleSubmit}
          title="Information Form"
          description="Complete the form below to submit your information to the system."
          submitLabel="Submit Information"
        />

        {/* Help Text */}
        <Paper p="md" withBorder bg="gray.0">
          <Title order={4} mb="xs">Need Help?</Title>
          <Text size="sm" c="dimmed">
            If you encounter any issues with this form or need assistance, please contact your system administrator.
            All submitted information will be securely stored and processed according to your company's data policies.
          </Text>
        </Paper>
      </Stack>
    </Container>
  );
}
