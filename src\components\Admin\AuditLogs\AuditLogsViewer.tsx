'use client';

import { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  Text,
  Group,
  Button,
  TextInput,
  Select,
  Paper,
  Badge,
  Alert,
  LoadingOverlay,
  Pagination,
  Table,
  Modal,
  JsonInput,
  Tabs,
  Code,
  ScrollArea,
} from '@mantine/core';
import {
  IconSearch,
  IconFilter,
  IconDownload,
  IconEye,
  IconAlertCircle,
  IconCheck,
  IconX,
  IconClock,
  IconUser,
  IconShield,
  IconTrash,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';

interface AuditLog {
  id: string;
  created_at: string;
  user_id: string | null;
  user_email: string;
  company_id: string | null;
  action: string;
  resource_type: string;
  resource_id: string | null;
  old_values: any;
  new_values: any;
  ip_address: string | null;
  user_agent: string | null;
  metadata: any;
}

interface AuditLogFilters {
  search: string;
  action: string;
  resourceType: string;
  userId: string;
  companyId: string;
  startDate: string;
  endDate: string;
  page: number;
  limit: number;
}

export function AuditLogsViewer() {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [detailsOpened, { open: openDetails, close: closeDetails }] = useDisclosure(false);
  const [filters, setFilters] = useState<AuditLogFilters>({
    search: '',
    action: '',
    resourceType: '',
    userId: '',
    companyId: '',
    startDate: '',
    endDate: '',
    page: 1,
    limit: 50
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0
  });
  const [clearLoading, setClearLoading] = useState(false);

  // Load audit logs
  const loadAuditLogs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value.toString());
      });

      const response = await fetch(`/api/admin/audit-logs?${params.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load audit logs');
      }

      const data = await response.json();
      setAuditLogs(data.data);
      setPagination(data.pagination);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load audit logs';
      setError(errorMessage);
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconX size={16} />
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAuditLogs();
  }, [filters]);

  // Handle filter changes
  const handleFilterChange = (key: keyof AuditLogFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // Handle view details
  const handleViewDetails = (log: AuditLog) => {
    setSelectedLog(log);
    openDetails();
  };

  // Handle export
  const handleExport = async () => {
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value && key !== 'page' && key !== 'limit') {
          params.append(key, value.toString());
        }
      });
      params.append('format', 'csv');
      params.append('limit', '10000');

      const response = await fetch(`/api/admin/audit-logs/export?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Export failed');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      notifications.show({
        title: 'Success',
        message: 'Audit logs exported successfully',
        color: 'green',
        icon: <IconCheck size={16} />
      });
    } catch (err) {
      notifications.show({
        title: 'Error',
        message: 'Failed to export audit logs',
        color: 'red',
        icon: <IconX size={16} />
      });
    }
  };

  // Handle clear logs
  const handleClearLogs = async () => {
    const confirmed = window.confirm(
      'Are you sure you want to clear all audit logs? This action cannot be undone and will permanently delete all audit log entries.'
    );

    if (!confirmed) return;

    try {
      setClearLoading(true);
      const response = await fetch('/api/admin/audit-logs/clear', {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to clear audit logs');
      }

      notifications.show({
        title: 'Success',
        message: 'All audit logs have been cleared successfully',
        color: 'green',
        icon: <IconCheck size={16} />
      });

      // Reload the logs (should be empty now)
      loadAuditLogs();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to clear audit logs',
        color: 'red',
        icon: <IconX size={16} />
      });
    } finally {
      setClearLoading(false);
    }
  };

  const getActionColor = (action: string) => {
    const colors: Record<string, string> = {
      CREATE: 'green',
      UPDATE: 'blue',
      DELETE: 'red',
      LOGIN: 'cyan',
      LOGOUT: 'gray',
      ACCESS: 'violet',
      ACTIVATE: 'green',
      DEACTIVATE: 'orange',
      EXPORT: 'indigo',
      IMPORT: 'teal'
    };
    return colors[action] || 'gray';
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatUserAgent = (userAgent: string | null) => {
    if (!userAgent) return 'Unknown';
    
    // Simple user agent parsing
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Other';
  };

  return (
    <Stack gap="md">
      {/* Header */}
      <Group justify="space-between">
        <div>
          <Title order={2}>Audit Logs</Title>
          <Text c="dimmed" size="sm">
            View and monitor all system activities and changes
          </Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />} onClick={handleExport}>
            Export Logs
          </Button>
          <Button
            variant="light"
            color="red"
            leftSection={<IconTrash size={16} />}
            onClick={handleClearLogs}
            loading={clearLoading}
          >
            Clear All Logs
          </Button>
        </Group>
      </Group>

      {/* Filters */}
      <Paper p="md" withBorder>
        <Stack gap="md">
          <Group>
            <TextInput
              placeholder="Search logs..."
              leftSection={<IconSearch size={16} />}
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              style={{ flex: 1 }}
            />
            <Select
              placeholder="Action"
              data={[
                { value: '', label: 'All Actions' },
                { value: 'CREATE', label: 'Create' },
                { value: 'UPDATE', label: 'Update' },
                { value: 'DELETE', label: 'Delete' },
                { value: 'LOGIN', label: 'Login' },
                { value: 'LOGOUT', label: 'Logout' },
                { value: 'ACCESS', label: 'Access' },
                { value: 'ACTIVATE', label: 'Activate' },
                { value: 'DEACTIVATE', label: 'Deactivate' },
                { value: 'EXPORT', label: 'Export' },
                { value: 'IMPORT', label: 'Import' }
              ]}
              value={filters.action}
              onChange={(value) => handleFilterChange('action', value || '')}
              clearable
            />
            <Select
              placeholder="Resource Type"
              data={[
                { value: '', label: 'All Resources' },
                { value: 'core_field', label: 'Core Field' },
                { value: 'custom_field', label: 'Custom Field' },
                { value: 'record', label: 'Record' },
                { value: 'company', label: 'Company' },
                { value: 'profile', label: 'Profile' },
                { value: 'admin_dashboard', label: 'Admin Dashboard' }
              ]}
              value={filters.resourceType}
              onChange={(value) => handleFilterChange('resourceType', value || '')}
              clearable
            />
          </Group>
          <Group>
            <TextInput
              placeholder="Start Date (YYYY-MM-DD)"
              value={filters.startDate}
              onChange={(e) => handleFilterChange('startDate', e.target.value)}
            />
            <TextInput
              placeholder="End Date (YYYY-MM-DD)"
              value={filters.endDate}
              onChange={(e) => handleFilterChange('endDate', e.target.value)}
            />
          </Group>
        </Stack>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
      )}

      {/* Audit Logs Table */}
      <Paper withBorder style={{ position: 'relative' }}>
        <LoadingOverlay visible={loading} />
        
        <ScrollArea>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Time</Table.Th>
                <Table.Th>User</Table.Th>
                <Table.Th>Action</Table.Th>
                <Table.Th>Resource</Table.Th>
                <Table.Th>IP Address</Table.Th>
                <Table.Th>Browser</Table.Th>
                <Table.Th>Details</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {auditLogs.map((log) => (
                <Table.Tr key={log.id}>
                  <Table.Td>
                    <Group gap="xs">
                      <IconClock size={14} />
                      <Text size="sm">{formatDateTime(log.created_at)}</Text>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <IconUser size={14} />
                      <div>
                        <Text size="sm">{log.user_email}</Text>
                        {log.user_id && (
                          <Text size="xs" c="dimmed">{log.user_id.substring(0, 8)}...</Text>
                        )}
                      </div>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Badge variant="light" color={getActionColor(log.action)}>
                      {log.action}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <div>
                      <Text size="sm">{log.resource_type}</Text>
                      {log.resource_id && (
                        <Text size="xs" c="dimmed">{log.resource_id.substring(0, 8)}...</Text>
                      )}
                    </div>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{log.ip_address || 'Unknown'}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{formatUserAgent(log.user_agent)}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Button
                      size="xs"
                      variant="light"
                      leftSection={<IconEye size={14} />}
                      onClick={() => handleViewDetails(log)}
                    >
                      View
                    </Button>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </ScrollArea>

        {auditLogs.length === 0 && !loading && (
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <Text c="dimmed">No audit logs found</Text>
          </div>
        )}
      </Paper>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <Group justify="center">
          <Pagination
            value={pagination.page}
            onChange={handlePageChange}
            total={pagination.totalPages}
          />
        </Group>
      )}

      {/* Audit Log Details Modal */}
      <Modal
        opened={detailsOpened}
        onClose={closeDetails}
        title="Audit Log Details"
        size="lg"
      >
        {selectedLog && (
          <Tabs defaultValue="overview">
            <Tabs.List>
              <Tabs.Tab value="overview" leftSection={<IconShield size={16} />}>
                Overview
              </Tabs.Tab>
              <Tabs.Tab value="changes" leftSection={<IconEye size={16} />}>
                Changes
              </Tabs.Tab>
              <Tabs.Tab value="metadata" leftSection={<IconClock size={16} />}>
                Metadata
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="overview" pt="md">
              <Stack gap="md">
                <Group>
                  <Text fw={500}>Action:</Text>
                  <Badge color={getActionColor(selectedLog.action)}>
                    {selectedLog.action}
                  </Badge>
                </Group>
                <Group>
                  <Text fw={500}>User:</Text>
                  <Text>{selectedLog.user_email}</Text>
                </Group>
                <Group>
                  <Text fw={500}>Resource:</Text>
                  <Text>{selectedLog.resource_type}</Text>
                </Group>
                <Group>
                  <Text fw={500}>Time:</Text>
                  <Text>{formatDateTime(selectedLog.created_at)}</Text>
                </Group>
                <Group>
                  <Text fw={500}>IP Address:</Text>
                  <Text>{selectedLog.ip_address || 'Unknown'}</Text>
                </Group>
                <Group>
                  <Text fw={500}>User Agent:</Text>
                  <Text size="sm">{selectedLog.user_agent || 'Unknown'}</Text>
                </Group>
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="changes" pt="md">
              <Stack gap="md">
                {selectedLog.old_values && (
                  <div>
                    <Text fw={500} mb="xs">Previous Values:</Text>
                    <Code block>
                      {JSON.stringify(selectedLog.old_values, null, 2)}
                    </Code>
                  </div>
                )}
                {selectedLog.new_values && (
                  <div>
                    <Text fw={500} mb="xs">New Values:</Text>
                    <Code block>
                      {JSON.stringify(selectedLog.new_values, null, 2)}
                    </Code>
                  </div>
                )}
                {!selectedLog.old_values && !selectedLog.new_values && (
                  <Text c="dimmed">No change data available</Text>
                )}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="metadata" pt="md">
              <Stack gap="md">
                {selectedLog.metadata && Object.keys(selectedLog.metadata).length > 0 ? (
                  <Code block>
                    {JSON.stringify(selectedLog.metadata, null, 2)}
                  </Code>
                ) : (
                  <Text c="dimmed">No metadata available</Text>
                )}
              </Stack>
            </Tabs.Panel>
          </Tabs>
        )}
      </Modal>
    </Stack>
  );
}
