'use client'

import { App<PERSON><PERSON>, Burger, Group, Skeleton } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import { auth } from "auth"
import React from 'react'
import { Footer } from '../Footer'

import { HomeButton } from '../Buttons/HomeButton'
import { ThemeToggle } from '../Buttons/ThemeToggleButton'
import { UserMenu } from '../Buttons/UserMenu'
import { Navbar } from '../Buttons/NavBar/NavBar'
import { Logo } from '../Logo/Logo'
import { useSession } from "next-auth/react"
import { AdSenseBanner } from '../AdSense/AdSenseBanner'
import { useDeviceDetection } from "src/hooks/useDeviceDetection"
import { usePathname } from 'next/navigation'

export default function FullLayout({ children }: { children: React.ReactNode }) {
  const [opened, { toggle }] = useDisclosure()
  const { data: session } = useSession()
  const { isDesktop } = useDeviceDetection();
  const pathname = usePathname();
  const isAdminPage = pathname?.startsWith('/admin') ?? false;
  return (
    <AppShell
      layout="alt"
      header={{ height: 60 }}
      footer={{ height: 60 }}
      navbar={{ width: 300, breakpoint: 'sm', collapsed: { mobile: !opened } }}
      aside={isAdminPage ? undefined : { width: 300, breakpoint: 'md', collapsed: { desktop: false, mobile: true } }}
      padding="md"
    >
      <AppShell.Header>
        <Group h="100%" px="md" justify="space-between">
          <Group>
            <Burger opened={opened} onClick={toggle} hiddenFrom="sm" size="sm" />
         

        
          </Group>
        </Group>
      </AppShell.Header>
      <AppShell.Navbar p="md">
        <Group>
          <Burger opened={opened} onClick={toggle} hiddenFrom="sm" size="sm" />
          <Logo />
        
        </Group>
        <Navbar />

      </AppShell.Navbar>
      <AppShell.Main style={isAdminPage ? { gridColumn: '1 / -1' } : {}}>
        {children}
      </AppShell.Main>
      {!isAdminPage && (
        <AppShell.Aside p="md"> {isDesktop ? (
          <AdSenseBanner slot="9822011476" responsive={true} />
        ) : (
          <>No Sponsor</>
        )}
        </AppShell.Aside>
      )}
      <AppShell.Footer p="md"><Footer /></AppShell.Footer>
    </AppShell>
  )
}
