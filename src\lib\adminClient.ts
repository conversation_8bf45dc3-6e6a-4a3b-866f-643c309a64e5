/**
 * Client-side admin utilities
 */

export interface AdminAccessInfo {
  isAuthorized: boolean;
  isSuperAdmin: boolean;
  error?: string;
}

/**
 * Check admin access level for the current user
 */
export async function checkAdminAccess(): Promise<AdminAccessInfo> {
  try {
    const response = await fetch('/api/admin/access-check');
    
    if (!response.ok) {
      return {
        isAuthorized: false,
        isSuperAdmin: false,
        error: 'Failed to check admin access'
      };
    }

    const data = await response.json();
    return {
      isAuthorized: data.isAuthorized || false,
      isSuperAdmin: data.isSuperAdmin || false,
      error: data.error
    };
  } catch (error) {
    return {
      isAuthorized: false,
      isSuperAdmin: false,
      error: 'Network error checking admin access'
    };
  }
}
