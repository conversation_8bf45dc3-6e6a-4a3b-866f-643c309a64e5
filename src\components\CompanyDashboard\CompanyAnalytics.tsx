'use client';

import { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  Text,
  Group,
  Paper,
  SimpleGrid,
  Card,
  Badge,
  Alert,
  LoadingOverlay,
  Select,
  Button,
  Progress,
} from '@mantine/core';
import {
  IconChartBar,
  IconTrendingUp,
  IconUsers,
  IconDatabase,
  IconCalendar,
  IconDownload,
  IconAlertCircle,
} from '@tabler/icons-react';

interface CompanyAnalytics {
  overview: {
    totalRecords: number;
    totalUsers: number;
    recordsThisMonth: number;
    recordsLastMonth: number;
    growthPercentage: number;
  };
  recordsByStatus: Record<string, number>;
  recordsByMonth: Array<{
    month: string;
    count: number;
  }>;
  topUsers: Array<{
    user_name: string;
    user_email: string;
    record_count: number;
  }>;
  fieldUsage: Array<{
    field_name: string;
    field_type: string;
    usage_count: number;
    usage_percentage: number;
  }>;
}

export function CompanyAnalytics() {
  const [analytics, setAnalytics] = useState<CompanyAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('30');

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        timeRange: timeRange,
        companyLevel: 'true'
      });

      const response = await fetch(`/api/company/analytics?${params}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load analytics');
      }

      const data = await response.json();
      setAnalytics(data.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load analytics';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAnalytics();
  }, [timeRange]);

  const handleExportReport = async () => {
    try {
      const params = new URLSearchParams({
        timeRange: timeRange,
        format: 'xlsx'
      });

      const response = await fetch(`/api/company/analytics/export?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to export report');
      }

      // Handle file download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `company-analytics-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting report:', error);
    }
  };

  if (loading) {
    return (
      <div style={{ position: 'relative', minHeight: 400 }}>
        <LoadingOverlay visible />
      </div>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
        {error}
      </Alert>
    );
  }

  if (!analytics) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} title="No Data" color="yellow">
        No analytics data available
      </Alert>
    );
  }

  return (
    <Stack gap="md">
      {/* Header */}
      <Group justify="space-between">
        <div>
          <Title order={3}>Company Analytics</Title>
          <Text c="dimmed">Insights and reports for your company data</Text>
        </div>
        <Group>
          <Select
            value={timeRange}
            onChange={(value) => setTimeRange(value || '30')}
            data={[
              { value: '7', label: 'Last 7 days' },
              { value: '30', label: 'Last 30 days' },
              { value: '90', label: 'Last 90 days' },
              { value: '365', label: 'Last year' },
            ]}
          />
          <Button
            variant="outline"
            leftSection={<IconDownload size={16} />}
            onClick={handleExportReport}
          >
            Export Report
          </Button>
        </Group>
      </Group>

      {/* Overview Metrics */}
      <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="md">
        <Card withBorder p="lg">
          <Group justify="space-between" mb="md">
            <IconDatabase size={24} color="blue" />
            <Badge color="blue">Total</Badge>
          </Group>
          <Title order={3} mb="xs">{analytics.overview.totalRecords}</Title>
          <Text c="dimmed" size="sm">Total Records</Text>
        </Card>

        <Card withBorder p="lg">
          <Group justify="space-between" mb="md">
            <IconUsers size={24} color="green" />
            <Badge color="green">Active</Badge>
          </Group>
          <Title order={3} mb="xs">{analytics.overview.totalUsers}</Title>
          <Text c="dimmed" size="sm">Team Members</Text>
        </Card>

        <Card withBorder p="lg">
          <Group justify="space-between" mb="md">
            <IconCalendar size={24} color="orange" />
            <Badge color="orange">This Month</Badge>
          </Group>
          <Title order={3} mb="xs">{analytics.overview.recordsThisMonth}</Title>
          <Text c="dimmed" size="sm">New Records</Text>
        </Card>

        <Card withBorder p="lg">
          <Group justify="space-between" mb="md">
            <IconTrendingUp size={24} color="violet" />
            <Badge 
              color={analytics.overview.growthPercentage >= 0 ? 'green' : 'red'}
            >
              {analytics.overview.growthPercentage >= 0 ? '+' : ''}{analytics.overview.growthPercentage}%
            </Badge>
          </Group>
          <Title order={3} mb="xs">Growth</Title>
          <Text c="dimmed" size="sm">vs Last Month</Text>
        </Card>
      </SimpleGrid>

      {/* Records by Status */}
      <Paper withBorder p="lg">
        <Title order={4} mb="md">Records by Status</Title>
        <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="md">
          {Object.entries(analytics.recordsByStatus).map(([status, count]) => (
            <div key={status}>
              <Group justify="space-between" mb="xs">
                <Text tt="capitalize">{status}</Text>
                <Text fw={500}>{count}</Text>
              </Group>
              <Progress
                value={(count / analytics.overview.totalRecords) * 100}
                size="sm"
                color={status === 'active' ? 'green' : status === 'archived' ? 'yellow' : 'gray'}
              />
            </div>
          ))}
        </SimpleGrid>
      </Paper>

      {/* Top Users */}
      <Paper withBorder p="lg">
        <Title order={4} mb="md">Top Contributors</Title>
        <Stack gap="md">
          {analytics.topUsers.slice(0, 5).map((user, index) => (
            <Group key={user.user_email} justify="space-between">
              <Group>
                <Badge variant="light" size="lg">
                  #{index + 1}
                </Badge>
                <div>
                  <Text fw={500}>{user.user_name || 'No name'}</Text>
                  <Text size="sm" c="dimmed">{user.user_email}</Text>
                </div>
              </Group>
              <Badge color="blue" size="lg">
                {user.record_count} records
              </Badge>
            </Group>
          ))}
        </Stack>
      </Paper>

      {/* Field Usage */}
      <Paper withBorder p="lg">
        <Title order={4} mb="md">Field Usage Statistics</Title>
        <Stack gap="md">
          {analytics.fieldUsage.slice(0, 10).map((field) => (
            <div key={field.field_name}>
              <Group justify="space-between" mb="xs">
                <Group>
                  <Text fw={500}>{field.field_name}</Text>
                  <Badge size="sm" variant="light">{field.field_type}</Badge>
                </Group>
                <Text size="sm" c="dimmed">
                  {field.usage_count} uses ({field.usage_percentage}%)
                </Text>
              </Group>
              <Progress
                value={field.usage_percentage}
                size="sm"
                color="blue"
              />
            </div>
          ))}
        </Stack>
      </Paper>

      {/* Monthly Trend */}
      <Paper withBorder p="lg">
        <Title order={4} mb="md">Monthly Record Creation Trend</Title>
        <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="md">
          {analytics.recordsByMonth.slice(-6).map((month) => (
            <Card key={month.month} withBorder>
              <Text size="sm" c="dimmed" mb="xs">{month.month}</Text>
              <Title order={3}>{month.count}</Title>
              <Text size="sm" c="dimmed">records created</Text>
            </Card>
          ))}
        </SimpleGrid>
      </Paper>

      {/* Help Text */}
      <Paper p="md" withBorder bg="gray.0">
        <Stack gap="xs">
          <Title order={4}>About Analytics</Title>
          <Text size="sm" c="dimmed">
            • Analytics are updated in real-time as your team creates and manages records
          </Text>
          <Text size="sm" c="dimmed">
            • Export reports to Excel for detailed analysis and sharing with stakeholders
          </Text>
          <Text size="sm" c="dimmed">
            • Use different time ranges to analyze trends and patterns in your data
          </Text>
        </Stack>
      </Paper>
    </Stack>
  );
}
