import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';

/**
 * GET /api/user/invitations
 * Get pending invitations for the current user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    // Get pending invitations for this user
    const { data: invitations, error: invitationsError } = await supabase
      .from('company_invitations')
      .select(`
        id,
        created_at,
        email,
        role,
        is_company_admin,
        status,
        expires_at,
        metadata,
        company:company_id(
          id,
          name,
          logo_url,
          description,
          subscription_plan
        ),
        invited_by_user:invited_by(
          full_name,
          email
        )
      `)
      .eq('email', userEmail.toLowerCase())
      .eq('status', 'pending')
      .gte('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false });

    if (invitationsError) {
      console.error('Error fetching user invitations:', invitationsError);
      return NextResponse.json({ error: 'Failed to fetch invitations' }, { status: 500 });
    }

    return NextResponse.json({
      data: invitations || []
    });

  } catch (error) {
    console.error('User invitations GET API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/user/invitations
 * Decline an invitation
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    const body = await request.json();
    const { invitation_id, action } = body;

    if (!invitation_id || !action) {
      return NextResponse.json({ error: 'Invitation ID and action are required' }, { status: 400 });
    }

    if (action !== 'decline') {
      return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    // Get the invitation
    const { data: invitation, error: invitationError } = await supabase
      .from('company_invitations')
      .select('id, email, status, company_id')
      .eq('id', invitation_id)
      .eq('email', userEmail.toLowerCase())
      .single();

    if (invitationError) {
      if (invitationError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Invitation not found' }, { status: 404 });
      }
      console.error('Error fetching invitation:', invitationError);
      return NextResponse.json({ error: 'Failed to fetch invitation' }, { status: 500 });
    }

    if (invitation.status !== 'pending') {
      return NextResponse.json({ error: 'Invitation is no longer pending' }, { status: 400 });
    }

    // Update invitation status to declined
    const { data: updatedInvitation, error: updateError } = await supabase
      .from('company_invitations')
      .update({
        status: 'declined',
        updated_at: new Date().toISOString()
      })
      .eq('id', invitation_id)
      .select(`
        id,
        created_at,
        updated_at,
        email,
        role,
        is_company_admin,
        status,
        expires_at,
        metadata,
        company:company_id(
          id,
          name,
          logo_url,
          description,
          subscription_plan
        ),
        invited_by_user:invited_by(
          full_name,
          email
        )
      `)
      .single();

    if (updateError) {
      console.error('Error updating invitation:', updateError);
      return NextResponse.json({ error: 'Failed to decline invitation' }, { status: 500 });
    }

    return NextResponse.json({
      data: updatedInvitation,
      message: 'Invitation declined successfully'
    });

  } catch (error) {
    console.error('User invitations POST API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
