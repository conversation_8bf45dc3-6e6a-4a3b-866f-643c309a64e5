'use client';

import { useState, useEffect } from 'react';
import {
  Stack,
  Group,
  Text,
  Button,
  TextInput,
  Select,
  Table,
  Paper,
  Badge,
  ActionIcon,
  Tooltip,
  Modal,
  LoadingOverlay,
  Pagination,
  Alert,
  Avatar,
  Menu,
  Checkbox,
  NumberInput,
  Textarea,
  Title,
  Divider,
  Card,
  Grid,
  Flex,
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconTrash,
  IconEye,
  IconBuilding,
  IconUsers,
  IconDatabase,
  IconSettings,
  IconDots,
  IconCheck,
  IconX,
  IconAlertCircle,
  IconRefresh,
  IconDownload,
  IconUpload,
} from '@tabler/icons-react';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { modals } from '@mantine/modals';

interface Company {
  id: string;
  created_at: string;
  updated_at: string | null;
  name: string;
  slug: string;
  domain: string | null;
  logo_url: string | null;
  description: string | null;
  subscription_plan: string;
  subscription_status: string;
  max_users: number;
  max_records: number;
  settings: any;
  disabled: boolean;
  user_count?: number;
  record_count?: number;
}

interface CompanyFormData {
  name: string;
  slug: string;
  domain: string;
  logo_url: string;
  description: string;
  subscription_plan: string;
  subscription_status: string;
  max_users: number;
  max_records: number;
}

export function CompanyManagement() {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCompanies, setSelectedCompanies] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [subscriptionPlanFilter, setSubscriptionPlanFilter] = useState('');
  const [subscriptionStatusFilter, setSubscriptionStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCompanies, setTotalCompanies] = useState(0);
  
  // Modal states
  const [createModalOpened, setCreateModalOpened] = useState(false);
  const [editModalOpened, setEditModalOpened] = useState(false);
  const [viewModalOpened, setViewModalOpened] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [modalLoading, setModalLoading] = useState(false);

  const createForm = useForm<CompanyFormData>({
    initialValues: {
      name: '',
      slug: '',
      domain: '',
      logo_url: '',
      description: '',
      subscription_plan: 'free',
      subscription_status: 'active',
      max_users: 10,
      max_records: 1000,
    },
    validate: {
      name: (value) => (!value ? 'Company name is required' : null),
      slug: (value) => {
        if (!value) return 'Slug is required';
        if (!/^[a-z0-9-_]+$/.test(value)) {
          return 'Slug must contain only lowercase letters, numbers, hyphens, and underscores';
        }
        return null;
      },
      max_users: (value) => (value < 1 ? 'Must be at least 1' : null),
      max_records: (value) => (value < 1 ? 'Must be at least 1' : null),
    },
  });

  const editForm = useForm<CompanyFormData>({
    initialValues: {
      name: '',
      slug: '',
      domain: '',
      logo_url: '',
      description: '',
      subscription_plan: 'free',
      subscription_status: 'active',
      max_users: 10,
      max_records: 1000,
    },
    validate: {
      name: (value) => (!value ? 'Company name is required' : null),
      slug: (value) => {
        if (!value) return 'Slug is required';
        if (!/^[a-z0-9-_]+$/.test(value)) {
          return 'Slug must contain only lowercase letters, numbers, hyphens, and underscores';
        }
        return null;
      },
      max_users: (value) => (value < 1 ? 'Must be at least 1' : null),
      max_records: (value) => (value < 1 ? 'Must be at least 1' : null),
    },
  });

  const fetchCompanies = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        ...(searchQuery && { search: searchQuery }),
        ...(subscriptionPlanFilter && { subscriptionPlan: subscriptionPlanFilter }),
        ...(subscriptionStatusFilter && { subscriptionStatus: subscriptionStatusFilter }),
      });

      const response = await fetch(`/api/admin/companies?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch companies');
      }

      const data = await response.json();
      setCompanies(data.data);
      setTotalPages(data.pagination.totalPages);
      setTotalCompanies(data.pagination.total);
    } catch (error) {
      console.error('Error fetching companies:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to fetch companies',
        color: 'red',
        icon: <IconX size={16} />,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCompanies();
  }, [currentPage, searchQuery, subscriptionPlanFilter, subscriptionStatusFilter]);

  const handleCreateCompany = async (values: CompanyFormData) => {
    try {
      setModalLoading(true);
      const response = await fetch('/api/admin/companies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create company');
      }

      notifications.show({
        title: 'Success',
        message: 'Company created successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      setCreateModalOpened(false);
      createForm.reset();
      fetchCompanies();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to create company',
        color: 'red',
        icon: <IconX size={16} />,
      });
    } finally {
      setModalLoading(false);
    }
  };

  const handleEditCompany = async (values: CompanyFormData) => {
    if (!selectedCompany) return;

    try {
      setModalLoading(true);
      const response = await fetch(`/api/admin/companies/${selectedCompany.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update company');
      }

      notifications.show({
        title: 'Success',
        message: 'Company updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      setEditModalOpened(false);
      setSelectedCompany(null);
      fetchCompanies();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to update company',
        color: 'red',
        icon: <IconX size={16} />,
      });
    } finally {
      setModalLoading(false);
    }
  };

  const handleDeleteCompany = (company: Company) => {
    modals.openConfirmModal({
      title: 'Delete Company',
      children: (
        <Text size="sm">
          Are you sure you want to delete <strong>{company.name}</strong>? This action cannot be undone and will remove all associated data.
        </Text>
      ),
      labels: { confirm: 'Delete', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          const response = await fetch(`/api/admin/companies/${company.id}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to delete company');
          }

          notifications.show({
            title: 'Success',
            message: 'Company deleted successfully',
            color: 'green',
            icon: <IconCheck size={16} />,
          });

          fetchCompanies();
        } catch (error) {
          notifications.show({
            title: 'Error',
            message: error instanceof Error ? error.message : 'Failed to delete company',
            color: 'red',
            icon: <IconX size={16} />,
          });
        }
      },
    });
  };

  const handleViewCompany = (company: Company) => {
    setSelectedCompany(company);
    setViewModalOpened(true);
  };

  const handleEditClick = (company: Company) => {
    setSelectedCompany(company);
    editForm.setValues({
      name: company.name,
      slug: company.slug,
      domain: company.domain || '',
      logo_url: company.logo_url || '',
      description: company.description || '',
      subscription_plan: company.subscription_plan,
      subscription_status: company.subscription_status,
      max_users: company.max_users,
      max_records: company.max_records,
    });
    setEditModalOpened(true);
  };

  const handleCompanySelect = (companyId: string, checked: boolean) => {
    const newSelected = new Set(selectedCompanies);
    if (checked) {
      newSelected.add(companyId);
    } else {
      newSelected.delete(companyId);
    }
    setSelectedCompanies(newSelected);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCompanies(new Set(companies.map(c => c.id)));
    } else {
      setSelectedCompanies(new Set());
    }
  };

  const getSubscriptionPlanColor = (plan: string) => {
    switch (plan) {
      case 'enterprise': return 'violet';
      case 'premium': return 'green';
      case 'basic': return 'blue';
      case 'free': return 'gray';
      default: return 'gray';
    }
  };

  const getSubscriptionStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'suspended': return 'yellow';
      case 'cancelled': return 'red';
      default: return 'gray';
    }
  };

  return (
    <Stack gap="md">
      {/* Header */}
      <Group justify="space-between">
        <div>
          <Title order={2}>Company Management</Title>
          <Text c="dimmed" size="sm">
            Manage tenant companies and their subscriptions ({totalCompanies} total)
          </Text>
        </div>
        <Button
          leftSection={<IconPlus size={16} />}
          onClick={() => setCreateModalOpened(true)}
        >
          Create Company
        </Button>
      </Group>

      {/* Filters */}
      <Paper p="md" withBorder>
        <Grid>
          <Grid.Col span={{ base: 12, md: 4 }}>
            <TextInput
              placeholder="Search companies..."
              leftSection={<IconSearch size={16} />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.currentTarget.value)}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 4 }}>
            <Select
              placeholder="Filter by plan"
              data={[
                { value: '', label: 'All Plans' },
                { value: 'free', label: 'Free' },
                { value: 'basic', label: 'Basic' },
                { value: 'premium', label: 'Premium' },
                { value: 'enterprise', label: 'Enterprise' },
              ]}
              value={subscriptionPlanFilter}
              onChange={(value) => setSubscriptionPlanFilter(value || '')}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 4 }}>
            <Select
              placeholder="Filter by status"
              data={[
                { value: '', label: 'All Statuses' },
                { value: 'active', label: 'Active' },
                { value: 'suspended', label: 'Suspended' },
                { value: 'cancelled', label: 'Cancelled' },
              ]}
              value={subscriptionStatusFilter}
              onChange={(value) => setSubscriptionStatusFilter(value || '')}
            />
          </Grid.Col>
        </Grid>
      </Paper>

      {/* Companies Table */}
      <Paper withBorder>
        <LoadingOverlay visible={loading} />

        {selectedCompanies.size > 0 && (
          <Alert color="blue" mb="md">
            <Group justify="space-between">
              <Text size="sm">
                {selectedCompanies.size} companies selected
              </Text>
              <Group gap="xs">
                <Button size="xs" variant="outline" color="red">
                  Bulk Delete
                </Button>
                <Button size="xs" variant="outline">
                  Bulk Export
                </Button>
              </Group>
            </Group>
          </Alert>
        )}

        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>
                <Checkbox
                  checked={selectedCompanies.size === companies.length && companies.length > 0}
                  indeterminate={selectedCompanies.size > 0 && selectedCompanies.size < companies.length}
                  onChange={(e) => handleSelectAll(e.currentTarget.checked)}
                />
              </Table.Th>
              <Table.Th>Company</Table.Th>
              <Table.Th>Subscription</Table.Th>
              <Table.Th>Limits</Table.Th>
              <Table.Th>Usage</Table.Th>
              <Table.Th>Status</Table.Th>
              <Table.Th>Created</Table.Th>
              <Table.Th>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {companies.map((company) => (
              <Table.Tr key={company.id}>
                <Table.Td>
                  <Checkbox
                    checked={selectedCompanies.has(company.id)}
                    onChange={(e) => handleCompanySelect(company.id, e.currentTarget.checked)}
                  />
                </Table.Td>
                <Table.Td>
                  <Group gap="sm">
                    <Avatar
                      src={company.logo_url}
                      size="sm"
                      radius="sm"
                    >
                      <IconBuilding size={16} />
                    </Avatar>
                    <div>
                      <Text fw={500}>{company.name}</Text>
                      <Text size="xs" c="dimmed">{company.slug}</Text>
                      {company.domain && (
                        <Text size="xs" c="dimmed">{company.domain}</Text>
                      )}
                    </div>
                  </Group>
                </Table.Td>
                <Table.Td>
                  <Stack gap="xs">
                    <Badge
                      size="sm"
                      variant="light"
                      color={getSubscriptionPlanColor(company.subscription_plan)}
                    >
                      {company.subscription_plan}
                    </Badge>
                    <Badge
                      size="xs"
                      variant="outline"
                      color={getSubscriptionStatusColor(company.subscription_status)}
                    >
                      {company.subscription_status}
                    </Badge>
                  </Stack>
                </Table.Td>
                <Table.Td>
                  <Stack gap="xs">
                    <Group gap="xs">
                      <IconUsers size={12} />
                      <Text size="xs">{company.max_users} users</Text>
                    </Group>
                    <Group gap="xs">
                      <IconDatabase size={12} />
                      <Text size="xs">{company.max_records} records</Text>
                    </Group>
                  </Stack>
                </Table.Td>
                <Table.Td>
                  <Stack gap="xs">
                    <Text size="xs" c="dimmed">
                      {company.user_count || 0} / {company.max_users} users
                    </Text>
                    <Text size="xs" c="dimmed">
                      {company.record_count || 0} / {company.max_records} records
                    </Text>
                  </Stack>
                </Table.Td>
                <Table.Td>
                  <Badge
                    variant="light"
                    color={company.disabled ? 'red' : 'green'}
                  >
                    {company.disabled ? 'Disabled' : 'Active'}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">
                    {new Date(company.created_at).toLocaleDateString()}
                  </Text>
                </Table.Td>
                <Table.Td>
                  <Group gap="xs">
                    <Tooltip label="View Details">
                      <ActionIcon
                        variant="subtle"
                        color="blue"
                        onClick={() => handleViewCompany(company)}
                      >
                        <IconEye size={16} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="Edit">
                      <ActionIcon
                        variant="subtle"
                        color="orange"
                        onClick={() => handleEditClick(company)}
                      >
                        <IconEdit size={16} />
                      </ActionIcon>
                    </Tooltip>
                    <Menu shadow="md" width={200}>
                      <Menu.Target>
                        <ActionIcon variant="subtle" color="gray">
                          <IconDots size={16} />
                        </ActionIcon>
                      </Menu.Target>
                      <Menu.Dropdown>
                        <Menu.Item
                          leftSection={<IconSettings size={14} />}
                          onClick={() => handleViewCompany(company)}
                        >
                          Settings
                        </Menu.Item>
                        <Menu.Item
                          leftSection={<IconDownload size={14} />}
                        >
                          Export Data
                        </Menu.Item>
                        <Menu.Divider />
                        <Menu.Item
                          leftSection={<IconTrash size={14} />}
                          color="red"
                          onClick={() => handleDeleteCompany(company)}
                        >
                          Delete
                        </Menu.Item>
                      </Menu.Dropdown>
                    </Menu>
                  </Group>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>

        {companies.length === 0 && !loading && (
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <IconBuilding size={48} color="gray" />
            <Text c="dimmed" mt="md">
              No companies found
            </Text>
          </div>
        )}

        {totalPages > 1 && (
          <Group justify="center" mt="md" pb="md">
            <Pagination
              value={currentPage}
              onChange={setCurrentPage}
              total={totalPages}
            />
          </Group>
        )}
      </Paper>

      {/* Create Company Modal */}
      <Modal
        opened={createModalOpened}
        onClose={() => setCreateModalOpened(false)}
        title="Create New Company"
        size="lg"
      >
        <LoadingOverlay visible={modalLoading} />
        <form onSubmit={createForm.onSubmit(handleCreateCompany)}>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Company Name"
                  placeholder="Enter company name"
                  required
                  {...createForm.getInputProps('name')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Slug"
                  placeholder="company-slug"
                  required
                  {...createForm.getInputProps('slug')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Domain"
                  placeholder="company.com"
                  {...createForm.getInputProps('domain')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Logo URL"
                  placeholder="https://..."
                  {...createForm.getInputProps('logo_url')}
                />
              </Grid.Col>
            </Grid>

            <Textarea
              label="Description"
              placeholder="Brief description of the company"
              rows={3}
              {...createForm.getInputProps('description')}
            />

            <Grid>
              <Grid.Col span={6}>
                <Select
                  label="Subscription Plan"
                  data={[
                    { value: 'free', label: 'Free' },
                    { value: 'basic', label: 'Basic' },
                    { value: 'premium', label: 'Premium' },
                    { value: 'enterprise', label: 'Enterprise' },
                  ]}
                  {...createForm.getInputProps('subscription_plan')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Subscription Status"
                  data={[
                    { value: 'active', label: 'Active' },
                    { value: 'suspended', label: 'Suspended' },
                    { value: 'cancelled', label: 'Cancelled' },
                  ]}
                  {...createForm.getInputProps('subscription_status')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label="Max Users"
                  min={1}
                  {...createForm.getInputProps('max_users')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label="Max Records"
                  min={1}
                  {...createForm.getInputProps('max_records')}
                />
              </Grid.Col>
            </Grid>

            <Group justify="flex-end" mt="md">
              <Button
                variant="outline"
                onClick={() => setCreateModalOpened(false)}
                disabled={modalLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                loading={modalLoading}
                leftSection={<IconBuilding size={16} />}
              >
                Create Company
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Edit Company Modal */}
      <Modal
        opened={editModalOpened}
        onClose={() => setEditModalOpened(false)}
        title="Edit Company"
        size="lg"
      >
        <LoadingOverlay visible={modalLoading} />
        <form onSubmit={editForm.onSubmit(handleEditCompany)}>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Company Name"
                  placeholder="Enter company name"
                  required
                  {...editForm.getInputProps('name')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Slug"
                  placeholder="company-slug"
                  required
                  {...editForm.getInputProps('slug')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Domain"
                  placeholder="company.com"
                  {...editForm.getInputProps('domain')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Logo URL"
                  placeholder="https://..."
                  {...editForm.getInputProps('logo_url')}
                />
              </Grid.Col>
            </Grid>

            <Textarea
              label="Description"
              placeholder="Brief description of the company"
              rows={3}
              {...editForm.getInputProps('description')}
            />

            <Grid>
              <Grid.Col span={6}>
                <Select
                  label="Subscription Plan"
                  data={[
                    { value: 'free', label: 'Free' },
                    { value: 'basic', label: 'Basic' },
                    { value: 'premium', label: 'Premium' },
                    { value: 'enterprise', label: 'Enterprise' },
                  ]}
                  {...editForm.getInputProps('subscription_plan')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Subscription Status"
                  data={[
                    { value: 'active', label: 'Active' },
                    { value: 'suspended', label: 'Suspended' },
                    { value: 'cancelled', label: 'Cancelled' },
                  ]}
                  {...editForm.getInputProps('subscription_status')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label="Max Users"
                  min={1}
                  {...editForm.getInputProps('max_users')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label="Max Records"
                  min={1}
                  {...editForm.getInputProps('max_records')}
                />
              </Grid.Col>
            </Grid>

            <Group justify="flex-end" mt="md">
              <Button
                variant="outline"
                onClick={() => setEditModalOpened(false)}
                disabled={modalLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                loading={modalLoading}
                leftSection={<IconEdit size={16} />}
              >
                Update Company
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* View Company Modal */}
      <Modal
        opened={viewModalOpened}
        onClose={() => setViewModalOpened(false)}
        title="Company Details"
        size="lg"
      >
        {selectedCompany && (
          <Stack gap="md">
            <Group>
              <Avatar
                src={selectedCompany.logo_url}
                size="lg"
                radius="sm"
              >
                <IconBuilding size={24} />
              </Avatar>
              <div>
                <Title order={3}>{selectedCompany.name}</Title>
                <Text c="dimmed">{selectedCompany.slug}</Text>
                {selectedCompany.domain && (
                  <Text size="sm" c="blue">
                    {selectedCompany.domain}
                  </Text>
                )}
              </div>
            </Group>

            {selectedCompany.description && (
              <div>
                <Text fw={500} mb="xs">Description</Text>
                <Text size="sm" c="dimmed">{selectedCompany.description}</Text>
              </div>
            )}

            <Divider />

            <Grid>
              <Grid.Col span={6}>
                <Card withBorder>
                  <Stack gap="xs">
                    <Text fw={500} size="sm">Subscription</Text>
                    <Badge
                      size="lg"
                      variant="light"
                      color={getSubscriptionPlanColor(selectedCompany.subscription_plan)}
                    >
                      {selectedCompany.subscription_plan}
                    </Badge>
                    <Badge
                      size="sm"
                      variant="outline"
                      color={getSubscriptionStatusColor(selectedCompany.subscription_status)}
                    >
                      {selectedCompany.subscription_status}
                    </Badge>
                  </Stack>
                </Card>
              </Grid.Col>
              <Grid.Col span={6}>
                <Card withBorder>
                  <Stack gap="xs">
                    <Text fw={500} size="sm">Limits</Text>
                    <Group gap="xs">
                      <IconUsers size={14} />
                      <Text size="sm">{selectedCompany.max_users} users</Text>
                    </Group>
                    <Group gap="xs">
                      <IconDatabase size={14} />
                      <Text size="sm">{selectedCompany.max_records} records</Text>
                    </Group>
                  </Stack>
                </Card>
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <div>
                  <Text fw={500} size="sm" mb="xs">Created</Text>
                  <Text size="sm" c="dimmed">
                    {new Date(selectedCompany.created_at).toLocaleString()}
                  </Text>
                </div>
              </Grid.Col>
              <Grid.Col span={6}>
                <div>
                  <Text fw={500} size="sm" mb="xs">Last Updated</Text>
                  <Text size="sm" c="dimmed">
                    {selectedCompany.updated_at
                      ? new Date(selectedCompany.updated_at).toLocaleString()
                      : 'Never'
                    }
                  </Text>
                </div>
              </Grid.Col>
            </Grid>

            <div>
              <Text fw={500} size="sm" mb="xs">Status</Text>
              <Badge
                variant="light"
                color={selectedCompany.disabled ? 'red' : 'green'}
              >
                {selectedCompany.disabled ? 'Disabled' : 'Active'}
              </Badge>
            </div>
          </Stack>
        )}
      </Modal>
    </Stack>
  );
}
