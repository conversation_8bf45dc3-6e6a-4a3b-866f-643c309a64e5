'use client';

import { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  Text,
  Group,
  Button,
  Select,
  Paper,
  SimpleGrid,
  LoadingOverlay,
  Alert,
  Badge,
  Progress,
  Table,
  ActionIcon,
  Menu,
} from '@mantine/core';
import {
  IconDownload,
  IconRefresh,
  IconTrendingUp,
  IconTrendingDown,
  IconMinus,
  IconUsers,
  IconBuilding,
  IconDatabase,
  IconChartBar,
  IconAlertCircle,
  IconCheck,
  IconX,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import {
  AnalyticsData,
  TimeRange,
  TIME_RANGE_LABELS,
  formatNumber,
  formatPercentage,
  calculateGrowthRate,
  getGrowthColor,
  formatDateTime,
} from 'src/types/analytics';
import {
  fetchAnalytics,
  downloadReport,
  downloadBlob,
  AnalyticsApiError
} from 'src/lib/analyticsApi';

interface MetricCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  growth?: number;
  subtitle?: string;
}

function MetricCard({ title, value, icon, color, growth, subtitle }: MetricCardProps) {
  const growthIcon = growth !== undefined ? (
    growth > 0 ? <IconTrendingUp size={14} /> :
    growth < 0 ? <IconTrendingDown size={14} /> :
    <IconMinus size={14} />
  ) : null;

  const growthColor = growth !== undefined ? getGrowthColor(growth) : undefined;

  return (
    <Paper withBorder p="md" radius="md">
      <Group justify="apart">
        <div>
          <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
            {title}
          </Text>
          <Text fw={700} fz="xl">
            {formatNumber(value)}
          </Text>
          {subtitle && (
            <Text size="xs" c="dimmed">
              {subtitle}
            </Text>
          )}
          {growth !== undefined && (
            <Group gap="xs" mt="xs">
              <Text size="sm" c={growthColor} fw={500}>
                {growthIcon}
                {Math.abs(growth)}%
              </Text>
              <Text size="xs" c="dimmed">
                vs previous period
              </Text>
            </Group>
          )}
        </div>
        <div style={{ color }}>
          {icon}
        </div>
      </Group>
    </Paper>
  );
}

export function AnalyticsDashboard() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<TimeRange>('30d');
  const [refreshing, setRefreshing] = useState(false);

  // Load analytics data
  const loadAnalytics = async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);
      
      const response = await fetchAnalytics(timeRange);
      setAnalytics(response.data);
    } catch (err) {
      const errorMessage = err instanceof AnalyticsApiError ? err.message : 'Failed to load analytics';
      setError(errorMessage);
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconX size={16} />
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadAnalytics();
  }, [timeRange]);

  // Handle refresh
  const handleRefresh = () => {
    loadAnalytics(true);
  };

  // Handle export
  const handleExport = async (reportType: 'records' | 'companies' | 'users' | 'fields') => {
    try {
      const blob = await downloadReport({
        type: reportType,
        format: 'csv',
        limit: 10000
      });
      
      const filename = `${reportType}-report-${new Date().toISOString().split('T')[0]}.csv`;
      downloadBlob(blob, filename);
      
      notifications.show({
        title: 'Success',
        message: `${reportType} report exported successfully`,
        color: 'green',
        icon: <IconCheck size={16} />
      });
    } catch (err) {
      notifications.show({
        title: 'Error',
        message: 'Failed to export report',
        color: 'red',
        icon: <IconX size={16} />
      });
    }
  };

  if (loading) {
    return (
      <div style={{ position: 'relative', minHeight: 400 }}>
        <LoadingOverlay visible />
      </div>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
        {error}
      </Alert>
    );
  }

  if (!analytics) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} title="No Data" color="yellow">
        No analytics data available
      </Alert>
    );
  }

  return (
    <Stack gap="md">
      {/* Header */}
      <Group justify="space-between">
        <div>
          <Title order={2}>Analytics Dashboard</Title>
          <Text c="dimmed" size="sm">
            System-wide analytics and insights
          </Text>
        </div>
        <Group>
          <Select
            value={timeRange}
            onChange={(value) => setTimeRange(value as TimeRange)}
            data={Object.entries(TIME_RANGE_LABELS).map(([value, label]) => ({ value, label }))}
            w={150}
          />
          <ActionIcon
            variant="light"
            onClick={handleRefresh}
            loading={refreshing}
          >
            <IconRefresh size={16} />
          </ActionIcon>
          <Menu shadow="md" width={200}>
            <Menu.Target>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                Export Reports
              </Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item onClick={() => handleExport('records')}>
                Records Report
              </Menu.Item>
              <Menu.Item onClick={() => handleExport('companies')}>
                Companies Report
              </Menu.Item>
              <Menu.Item onClick={() => handleExport('users')}>
                Users Report
              </Menu.Item>
              <Menu.Item onClick={() => handleExport('fields')}>
                Fields Report
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
      </Group>

      {/* Overview Metrics */}
      <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="md">
        <MetricCard
          title="Total Companies"
          value={analytics.overview.totalCompanies}
          icon={<IconBuilding size={24} />}
          color="#228be6"
          subtitle={`${analytics.overview.activeCompanies} active`}
        />
        <MetricCard
          title="Total Users"
          value={analytics.overview.totalProfiles}
          icon={<IconUsers size={24} />}
          color="#40c057"
          subtitle={`${analytics.overview.companyAdmins} admins`}
        />
        <MetricCard
          title="Total Records"
          value={analytics.overview.totalRecords}
          icon={<IconDatabase size={24} />}
          color="#fd7e14"
        />
        <MetricCard
          title="Core Fields"
          value={analytics.overview.totalCoreFields}
          icon={<IconChartBar size={24} />}
          color="#e64980"
          subtitle={`${analytics.overview.totalCustomFields} custom`}
        />
      </SimpleGrid>

      {/* Subscription Plans Distribution */}
      <Paper withBorder p="md">
        <Title order={3} mb="md">Subscription Plans</Title>
        <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="md">
          {Object.entries(analytics.subscriptionPlans).map(([plan, count]) => (
            <div key={plan}>
              <Group justify="space-between" mb="xs">
                <Text size="sm" tt="capitalize">{plan}</Text>
                <Text size="sm" fw={500}>{count}</Text>
              </Group>
              <Progress
                value={formatPercentage(count, analytics.overview.totalCompanies).replace('%', '') as any}
                size="sm"
                color={plan === 'enterprise' ? 'violet' : plan === 'premium' ? 'green' : plan === 'basic' ? 'blue' : 'gray'}
              />
            </div>
          ))}
        </SimpleGrid>
      </Paper>

      {/* Top Companies */}
      <Paper withBorder p="md">
        <Title order={3} mb="md">Top Companies by Activity</Title>
        <Table striped>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Company</Table.Th>
              <Table.Th>Plan</Table.Th>
              <Table.Th>Users</Table.Th>
              <Table.Th>Records</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {analytics.topCompanies.slice(0, 10).map((company) => (
              <Table.Tr key={company.id}>
                <Table.Td>
                  <div>
                    <Text fw={500}>{company.name}</Text>
                    <Text size="xs" c="dimmed">{company.slug}</Text>
                  </div>
                </Table.Td>
                <Table.Td>
                  <Badge
                    variant="light"
                    color={company.subscription_plan === 'enterprise' ? 'violet' : 
                           company.subscription_plan === 'premium' ? 'green' : 
                           company.subscription_plan === 'basic' ? 'blue' : 'gray'}
                  >
                    {company.subscription_plan}
                  </Badge>
                </Table.Td>
                <Table.Td>{company.profiles?.[0]?.count || 0}</Table.Td>
                <Table.Td>{company.records?.[0]?.count || 0}</Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </Paper>

      {/* Core Field Usage */}
      <Paper withBorder p="md">
        <Title order={3} mb="md">Core Field Usage</Title>
        <Table striped>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Field Name</Table.Th>
              <Table.Th>Type</Table.Th>
              <Table.Th>Usage</Table.Th>
              <Table.Th>Status</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {Object.values(analytics.coreFieldUsage)
              .sort((a, b) => b.usagePercentage - a.usagePercentage)
              .slice(0, 10)
              .map((usage) => (
                <Table.Tr key={usage.field.id}>
                  <Table.Td>
                    <Text fw={500}>{usage.field.name}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Badge variant="light" size="sm">
                      {usage.field.field_type}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <Progress
                        value={usage.usagePercentage}
                        size="sm"
                        style={{ flex: 1 }}
                        color={usage.usagePercentage > 70 ? 'green' : usage.usagePercentage > 30 ? 'yellow' : 'red'}
                      />
                      <Text size="sm">{usage.usagePercentage}%</Text>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Badge
                      variant="light"
                      color={usage.field.is_active ? 'green' : 'red'}
                      size="sm"
                    >
                      {usage.field.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </Table.Td>
                </Table.Tr>
              ))}
          </Table.Tbody>
        </Table>
      </Paper>

      {/* Recent Activity */}
      <Paper withBorder p="md">
        <Title order={3} mb="md">Recent Activity</Title>
        <Table striped>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Time</Table.Th>
              <Table.Th>User</Table.Th>
              <Table.Th>Action</Table.Th>
              <Table.Th>Resource</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {analytics.recentActivity.slice(0, 10).map((activity) => (
              <Table.Tr key={activity.id}>
                <Table.Td>
                  <Text size="sm">{formatDateTime(activity.created_at)}</Text>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">{activity.user_email}</Text>
                </Table.Td>
                <Table.Td>
                  <Badge variant="light" size="sm">
                    {activity.action}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">{activity.resource_type}</Text>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </Paper>
    </Stack>
  );
}
