import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { verifyAdminAuth } from 'src/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const supabase = getSupabaseClient();

    // Only super admin can access stats (simplified for profiles only)
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    // Get profiles count
    const { count: profilesCount, error: profilesError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });

    if (profilesError) {
      console.error('Error fetching profiles count:', profilesError);
      return NextResponse.json({ error: 'Failed to fetch profiles count' }, { status: 500 });
    }

    return NextResponse.json({
      profiles: profilesCount || 0,
    });

  } catch (error) {
    console.error('Admin stats API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
