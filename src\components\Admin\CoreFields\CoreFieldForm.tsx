'use client';

import { useState, useEffect } from 'react';
import {
  Stack,
  TextInput,
  Textarea,
  Select,
  Switch,
  NumberInput,
  Button,
  Group,
  Alert,
  Paper,
  Text,
  Divider,
  ActionIcon,
  Box,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconPlus,
  IconTrash,
  IconAlertCircle,
  IconCheck,
  IconX,
} from '@tabler/icons-react';
import {
  CoreField,
  CoreFieldFormData,
  FieldType,
  FIELD_TYPE_LABELS,
  FIELD_TYPE_DESCRIPTIONS,
  DEFAULT_VALIDATION_RULES,
  generateFieldKey,
  validateFieldKey,
} from 'src/types/coreFields';
import {
  createCoreField,
  updateCoreField,
  validateCoreFieldData,
  CoreFieldsApiError
} from 'src/lib/coreFieldsApi';

interface CoreFieldFormProps {
  field?: CoreField | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export function CoreFieldForm({ field, onSuccess, onCancel }: CoreFieldFormProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dropdownOptions, setDropdownOptions] = useState<string[]>(['']);

  const form = useForm<CoreFieldFormData>({
    initialValues: {
      name: field?.name || '',
      field_key: field?.field_key || '',
      field_type: field?.field_type || 'text',
      description: field?.description || '',
      is_required: field?.is_required || false,
      is_active: field?.is_active !== undefined ? field.is_active : true,
      default_value: field?.default_value || '',
      validation_rules: field?.validation_rules || {},
      dropdown_options: field?.dropdown_options || [],
      display_order: field?.display_order || 0,
    },
    validate: {
      name: (value) => (!value?.trim() ? 'Field name is required' : null),
      field_key: (value) => {
        if (!value?.trim()) return 'Field key is required';
        if (!validateFieldKey(value)) return 'Field key must contain only lowercase letters, numbers, and underscores';
        return null;
      },
      field_type: (value) => (!value ? 'Field type is required' : null),
    },
  });

  // Initialize dropdown options from field data
  useEffect(() => {
    if (field?.dropdown_options) {
      setDropdownOptions([...field.dropdown_options, '']);
    }
  }, [field]);

  // Auto-generate field key from name
  const handleNameChange = (value: string) => {
    form.setFieldValue('name', value);
    if (!field) { // Only auto-generate for new fields
      const generatedKey = generateFieldKey(value);
      form.setFieldValue('field_key', generatedKey);
    }
  };

  // Handle field type change
  const handleFieldTypeChange = (value: FieldType | null) => {
    if (!value) return;
    
    form.setFieldValue('field_type', value);
    
    // Set default validation rules for the field type
    const defaultRules = DEFAULT_VALIDATION_RULES[value] || {};
    form.setFieldValue('validation_rules', defaultRules);
    
    // Initialize dropdown options if switching to dropdown
    if (value === 'dropdown' && dropdownOptions.length === 1 && dropdownOptions[0] === '') {
      setDropdownOptions(['Option 1', '']);
    }
  };

  // Handle dropdown options
  const addDropdownOption = () => {
    setDropdownOptions([...dropdownOptions, '']);
  };

  const updateDropdownOption = (index: number, value: string) => {
    const newOptions = [...dropdownOptions];
    newOptions[index] = value;
    setDropdownOptions(newOptions);
  };

  const removeDropdownOption = (index: number) => {
    if (dropdownOptions.length > 1) {
      const newOptions = dropdownOptions.filter((_, i) => i !== index);
      setDropdownOptions(newOptions);
    }
  };

  // Handle form submission
  const handleSubmit = async (values: CoreFieldFormData) => {
    try {
      setLoading(true);
      setError(null);

      // Prepare dropdown options
      let finalDropdownOptions: string[] | undefined;
      if (values.field_type === 'dropdown') {
        finalDropdownOptions = dropdownOptions.filter(option => option.trim() !== '');
        if (finalDropdownOptions.length === 0) {
          setError('Dropdown fields must have at least one option');
          return;
        }
      }

      const formData: CoreFieldFormData = {
        ...values,
        dropdown_options: finalDropdownOptions,
      };

      // Validate form data
      const validation = validateCoreFieldData(formData);
      if (!validation.isValid) {
        setError(validation.errors.join(', '));
        return;
      }

      // Submit form
      if (field) {
        await updateCoreField(field.id, formData);
      } else {
        await createCoreField(formData);
      }

      onSuccess();
    } catch (err) {
      const errorMessage = err instanceof CoreFieldsApiError ? err.message : 'Failed to save core field';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={form.onSubmit(handleSubmit)}>
      <Stack gap="md">
        {/* Error Alert */}
        {error && (
          <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
            {error}
          </Alert>
        )}

        {/* Basic Information */}
        <Paper p="md" withBorder>
          <Stack gap="md">
            <Text fw={500} size="sm">Basic Information</Text>
            
            <TextInput
              label="Field Name"
              placeholder="Enter field name"
              required
              {...form.getInputProps('name')}
              onChange={(e) => handleNameChange(e.target.value)}
            />

            <TextInput
              label="Field Key"
              placeholder="field_key"
              description="Unique identifier for programmatic access (lowercase, numbers, underscores only)"
              required
              {...form.getInputProps('field_key')}
            />

            <Select
              label="Field Type"
              placeholder="Select field type"
              required
              data={Object.entries(FIELD_TYPE_LABELS).map(([value, label]) => ({
                value,
                label: `${label} - ${FIELD_TYPE_DESCRIPTIONS[value as FieldType]}`
              }))}
              {...form.getInputProps('field_type')}
              onChange={handleFieldTypeChange}
            />

            <Textarea
              label="Description"
              placeholder="Optional description for this field"
              {...form.getInputProps('description')}
            />
          </Stack>
        </Paper>

        {/* Field Options */}
        <Paper p="md" withBorder>
          <Stack gap="md">
            <Text fw={500} size="sm">Field Options</Text>
            
            <Group>
              <Switch
                label="Required Field"
                description="Users must fill this field"
                {...form.getInputProps('is_required', { type: 'checkbox' })}
              />
              
              <Switch
                label="Active"
                description="Field is available for use"
                {...form.getInputProps('is_active', { type: 'checkbox' })}
              />
            </Group>

            <TextInput
              label="Default Value"
              placeholder="Optional default value"
              {...form.getInputProps('default_value')}
            />

            <NumberInput
              label="Display Order"
              description="Order in which this field appears (lower numbers first)"
              min={0}
              {...form.getInputProps('display_order')}
            />
          </Stack>
        </Paper>

        {/* Dropdown Options */}
        {form.values.field_type === 'dropdown' && (
          <Paper p="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Text fw={500} size="sm">Dropdown Options</Text>
                <Button
                  size="xs"
                  variant="light"
                  leftSection={<IconPlus size={14} />}
                  onClick={addDropdownOption}
                >
                  Add Option
                </Button>
              </Group>
              
              {dropdownOptions.map((option, index) => (
                <Group key={index} align="flex-end">
                  <TextInput
                    placeholder={`Option ${index + 1}`}
                    value={option}
                    onChange={(e) => updateDropdownOption(index, e.target.value)}
                    style={{ flex: 1 }}
                  />
                  {dropdownOptions.length > 1 && (
                    <ActionIcon
                      color="red"
                      variant="subtle"
                      onClick={() => removeDropdownOption(index)}
                    >
                      <IconTrash size={16} />
                    </ActionIcon>
                  )}
                </Group>
              ))}
            </Stack>
          </Paper>
        )}

        {/* Form Actions */}
        <Group justify="flex-end">
          <Button variant="default" onClick={onCancel} disabled={loading}>
            Cancel
          </Button>
          <Button type="submit" loading={loading}>
            {field ? 'Update Field' : 'Create Field'}
          </Button>
        </Group>
      </Stack>
    </form>
  );
}
