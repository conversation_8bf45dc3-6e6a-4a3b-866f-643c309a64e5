export type SubscriptionPlan = 'free' | 'basic' | 'premium' | 'enterprise';
export type SubscriptionStatus = 'active' | 'suspended' | 'cancelled';

export interface Company {
  id: string;
  created_at: string;
  updated_at: string | null;
  name: string;
  slug: string;
  domain: string | null;
  logo_url: string | null;
  description: string | null;
  subscription_plan: SubscriptionPlan;
  subscription_status: SubscriptionStatus;
  max_users: number;
  max_records: number;
  settings: Record<string, any>;
  disabled: boolean;
  user_count?: number;
  record_count?: number;
  recent_users?: CompanyUser[];
}

export interface CompanyUser {
  id: string;
  full_name: string | null;
  email: string | null;
  created_at: string;
  last_login_at: string | null;
  is_company_admin: boolean;
}

export interface CompanyFormData {
  name: string;
  slug: string;
  domain?: string;
  logo_url?: string;
  description?: string;
  subscription_plan: SubscriptionPlan;
  subscription_status: SubscriptionStatus;
  max_users: number;
  max_records: number;
  settings?: Record<string, any>;
}

export interface CompaniesResponse {
  data: Company[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface CompanyFilters {
  search?: string;
  subscriptionPlan?: SubscriptionPlan | '';
  subscriptionStatus?: SubscriptionStatus | '';
  page?: number;
  limit?: number;
}

export const SUBSCRIPTION_PLAN_LABELS: Record<SubscriptionPlan, string> = {
  free: 'Free',
  basic: 'Basic',
  premium: 'Premium',
  enterprise: 'Enterprise'
};

export const SUBSCRIPTION_STATUS_LABELS: Record<SubscriptionStatus, string> = {
  active: 'Active',
  suspended: 'Suspended',
  cancelled: 'Cancelled'
};

export const SUBSCRIPTION_PLAN_COLORS: Record<SubscriptionPlan, string> = {
  free: 'gray',
  basic: 'blue',
  premium: 'green',
  enterprise: 'purple'
};

export const SUBSCRIPTION_STATUS_COLORS: Record<SubscriptionStatus, string> = {
  active: 'green',
  suspended: 'yellow',
  cancelled: 'red'
};

export const SUBSCRIPTION_PLAN_LIMITS: Record<SubscriptionPlan, { users: number; records: number; features: string[] }> = {
  free: {
    users: 5,
    records: 100,
    features: ['Basic fields', 'Basic reports']
  },
  basic: {
    users: 25,
    records: 1000,
    features: ['Custom fields', 'Advanced reports', 'Export data']
  },
  premium: {
    users: 100,
    records: 10000,
    features: ['All basic features', 'API access', 'Integrations', 'Priority support']
  },
  enterprise: {
    users: 1000,
    records: 100000,
    features: ['All premium features', 'Custom branding', 'SSO', 'Dedicated support']
  }
};

// Utility functions
export function generateCompanySlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-{2,}/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
}

export function validateCompanySlug(slug: string): boolean {
  return /^[a-z0-9-]+$/.test(slug) && slug.length > 0 && !slug.startsWith('-') && !slug.endsWith('-');
}

export function validateDomain(domain: string): boolean {
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
  return domainRegex.test(domain);
}

export function getSubscriptionPlanIcon(plan: SubscriptionPlan): string {
  const icons: Record<SubscriptionPlan, string> = {
    free: 'IconGift',
    basic: 'IconStar',
    premium: 'IconCrown',
    enterprise: 'IconBuilding'
  };
  return icons[plan];
}

export function getSubscriptionStatusIcon(status: SubscriptionStatus): string {
  const icons: Record<SubscriptionStatus, string> = {
    active: 'IconCheck',
    suspended: 'IconClock',
    cancelled: 'IconX'
  };
  return icons[status];
}

export function formatCompanyStats(company: Company): {
  userUsage: { current: number; max: number; percentage: number };
  recordUsage: { current: number; max: number; percentage: number };
} {
  const userUsage = {
    current: company.user_count || 0,
    max: company.max_users,
    percentage: Math.round(((company.user_count || 0) / company.max_users) * 100)
  };

  const recordUsage = {
    current: company.record_count || 0,
    max: company.max_records,
    percentage: Math.round(((company.record_count || 0) / company.max_records) * 100)
  };

  return { userUsage, recordUsage };
}

export function isCompanyAtLimit(company: Company): {
  usersAtLimit: boolean;
  recordsAtLimit: boolean;
  anyAtLimit: boolean;
} {
  const usersAtLimit = (company.user_count || 0) >= company.max_users;
  const recordsAtLimit = (company.record_count || 0) >= company.max_records;
  
  return {
    usersAtLimit,
    recordsAtLimit,
    anyAtLimit: usersAtLimit || recordsAtLimit
  };
}

export function getCompanyHealthStatus(company: Company): {
  status: 'healthy' | 'warning' | 'critical';
  color: string;
  message: string;
} {
  if (company.disabled) {
    return {
      status: 'critical',
      color: 'red',
      message: 'Company is disabled'
    };
  }

  if (company.subscription_status === 'cancelled') {
    return {
      status: 'critical',
      color: 'red',
      message: 'Subscription cancelled'
    };
  }

  if (company.subscription_status === 'suspended') {
    return {
      status: 'warning',
      color: 'yellow',
      message: 'Subscription suspended'
    };
  }

  const { userUsage, recordUsage } = formatCompanyStats(company);
  
  if (userUsage.percentage >= 90 || recordUsage.percentage >= 90) {
    return {
      status: 'warning',
      color: 'yellow',
      message: 'Approaching usage limits'
    };
  }

  if (userUsage.percentage >= 100 || recordUsage.percentage >= 100) {
    return {
      status: 'critical',
      color: 'red',
      message: 'Usage limits exceeded'
    };
  }

  return {
    status: 'healthy',
    color: 'green',
    message: 'All systems operational'
  };
}

export function canCompanyAddUsers(company: Company): boolean {
  return (company.user_count || 0) < company.max_users && 
         company.subscription_status === 'active' && 
         !company.disabled;
}

export function canCompanyAddRecords(company: Company): boolean {
  return (company.record_count || 0) < company.max_records && 
         company.subscription_status === 'active' && 
         !company.disabled;
}
