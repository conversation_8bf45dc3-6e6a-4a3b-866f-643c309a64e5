import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';
import { createAuditLog } from 'src/lib/auditLog';

interface CreateCompanyData {
  name: string;
  slug: string;
  description?: string;
  logo_url?: string;
}

/**
 * POST /api/company/create
 * Create a new company (for first-time users)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    // Check if user is already associated with a company
    if (profile.company_id) {
      return NextResponse.json({ error: 'User is already associated with a company' }, { status: 409 });
    }

    const body: CreateCompanyData = await request.json();
    const { name, slug, description, logo_url } = body;

    // Validation
    if (!name || name.trim().length === 0) {
      return NextResponse.json({ error: 'Company name is required' }, { status: 400 });
    }

    if (!slug || slug.trim().length === 0) {
      return NextResponse.json({ error: 'Company slug is required' }, { status: 400 });
    }

    // Validate slug format (alphanumeric, hyphens, underscores only)
    const slugRegex = /^[a-z0-9-_]+$/;
    if (!slugRegex.test(slug)) {
      return NextResponse.json({ 
        error: 'Slug must contain only lowercase letters, numbers, hyphens, and underscores' 
      }, { status: 400 });
    }

    // Check if slug already exists
    const { data: existingSlug } = await supabase
      .from('companies')
      .select('id')
      .eq('slug', slug)
      .single();

    if (existingSlug) {
      return NextResponse.json({ error: 'Company slug already exists' }, { status: 409 });
    }

    // Create the company
    const { data: newCompany, error: createError } = await supabase
      .from('companies')
      .insert({
        name: name.trim(),
        slug: slug.trim(),
        description: description?.trim() || null,
        logo_url: logo_url?.trim() || null,
        subscription_plan: 'free',
        subscription_status: 'active',
        max_users: 10,
        max_records: 1000,
        settings: {}
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating company:', createError);
      return NextResponse.json({ error: 'Failed to create company' }, { status: 500 });
    }

    // Update user profile to be company admin
    const { data: updatedProfile, error: updateProfileError } = await supabase
      .from('profiles')
      .update({
        company_id: newCompany.id,
        role: 'admin',
        is_company_admin: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', profile.id)
      .select()
      .single();

    if (updateProfileError) {
      console.error('Error updating user profile:', updateProfileError);
      // Try to rollback company creation
      await supabase
        .from('companies')
        .delete()
        .eq('id', newCompany.id);
      
      return NextResponse.json({ error: 'Failed to create company' }, { status: 500 });
    }

    // Create audit logs
    await Promise.all([
      // Log company creation
      createAuditLog({
        user_id: profile.id,
        user_email: userEmail,
        company_id: newCompany.id,
        action: 'CREATE',
        resource_type: 'company',
        resource_id: newCompany.id,
        new_values: newCompany,
        ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        user_agent: request.headers.get('user-agent'),
        metadata: { action: 'first_time_company_creation' }
      }),
      // Log profile update
      createAuditLog({
        user_id: profile.id,
        user_email: userEmail,
        company_id: newCompany.id,
        action: 'UPDATE',
        resource_type: 'profile',
        resource_id: profile.id,
        old_values: { company_id: null, role: 'user', is_company_admin: false },
        new_values: updatedProfile,
        ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        user_agent: request.headers.get('user-agent'),
        metadata: { action: 'become_company_admin' }
      })
    ]);

    return NextResponse.json({
      data: {
        company: newCompany,
        profile: updatedProfile
      },
      message: 'Company created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create company API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
