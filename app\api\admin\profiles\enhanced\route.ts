import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { verifyAdminAuth } from 'src/lib/adminAuth';
import { createAuditLog } from 'src/lib/auditLog';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can access enhanced profiles
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';
    const companyId = searchParams.get('companyId') || '';
    const role = searchParams.get('role') || '';
    const isCompanyAdmin = searchParams.get('isCompanyAdmin');
    const disabled = searchParams.get('disabled');
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const offset = (page - 1) * limit;

    const supabase = getSupabaseClient();

    // Build query with enhanced data
    let query = supabase
      .from('profiles')
      .select(`
        id,
        created_at,
        updated_at,
        full_name,
        email,
        avatar_url,
        disabled,
        company_id,
        role,
        is_company_admin,
        last_login_at,
        timezone,
        companies:company_id(
          id,
          name,
          slug,
          subscription_plan,
          subscription_status
        )
      `, { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`full_name.ilike.%${search}%,email.ilike.%${search}%`);
    }

    if (companyId) {
      query = query.eq('company_id', companyId);
    }

    if (role) {
      query = query.eq('role', role);
    }

    if (isCompanyAdmin !== null && isCompanyAdmin !== '') {
      query = query.eq('is_company_admin', isCompanyAdmin === 'true');
    }

    if (disabled !== null && disabled !== '') {
      query = query.eq('disabled', disabled === 'true');
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Get total count for pagination
    const { count } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });

    // Get paginated results
    const { data: profiles, error: profilesError } = await query
      .range(offset, offset + limit - 1);

    if (profilesError) {
      console.error('Error fetching enhanced profiles:', profilesError);
      return NextResponse.json({ error: 'Failed to fetch profiles' }, { status: 500 });
    }

    // Get additional statistics for each profile
    const profileIds = profiles.map(profile => profile.id);
    let recordCounts: { [key: string]: number } = {};
    
    if (profileIds.length > 0) {
      const { data: recordCountsData } = await supabase
        .from('records')
        .select('created_by')
        .in('created_by', profileIds)
        .eq('status', 'active');

      if (recordCountsData) {
        recordCounts = recordCountsData.reduce((acc, record) => {
          acc[record.created_by] = (acc[record.created_by] || 0) + 1;
          return acc;
        }, {} as { [key: string]: number });
      }
    }

    // Enrich profiles with additional data
    const enrichedProfiles = profiles.map((profile: any) => ({
      ...profile,
      record_count: recordCounts[profile.id] || 0,
      company_name: profile.companies?.name || null,
      company_slug: profile.companies?.slug || null,
      subscription_plan: profile.companies?.subscription_plan || null,
      subscription_status: profile.companies?.subscription_status || null
    }));

    const totalPages = Math.ceil((count || 0) / limit);

    // Get summary statistics
    const stats = {
      total: count || 0,
      active: profiles.filter(p => !p.disabled).length,
      disabled: profiles.filter(p => p.disabled).length,
      companyAdmins: profiles.filter(p => p.is_company_admin).length,
      withoutCompany: profiles.filter(p => !p.company_id).length,
      byRole: profiles.reduce((acc, profile) => {
        acc[profile.role] = (acc[profile.role] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    return NextResponse.json({
      data: enrichedProfiles,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages
      },
      stats
    });

  } catch (error) {
    console.error('Enhanced profiles API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can perform bulk operations
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { action, profileIds, data } = body;

    if (!action || !profileIds || !Array.isArray(profileIds)) {
      return NextResponse.json({ 
        error: 'Missing required fields: action, profileIds' 
      }, { status: 400 });
    }

    const supabase = getSupabaseClient();
    const userId = authResult.session?.user?.id;
    const userEmail = authResult.session?.user?.email || '';

    let results: any[] = [];
    let errors: any[] = [];

    switch (action) {
      case 'bulk_disable':
        for (const profileId of profileIds) {
          try {
            const { data: updatedProfile, error } = await supabase
              .from('profiles')
              .update({ disabled: true })
              .eq('id', profileId)
              .select()
              .single();

            if (error) throw error;

            results.push(updatedProfile);

            // Create audit log
            await createAuditLog({
              user_id: userId,
              user_email: userEmail,
              action: 'DISABLE',
              resource_type: 'profile',
              resource_id: profileId,
              new_values: { disabled: true },
              ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
              user_agent: request.headers.get('user-agent')
            });
          } catch (err) {
            errors.push({ profileId, error: err instanceof Error ? err.message : 'Unknown error' });
          }
        }
        break;

      case 'bulk_enable':
        for (const profileId of profileIds) {
          try {
            const { data: updatedProfile, error } = await supabase
              .from('profiles')
              .update({ disabled: false })
              .eq('id', profileId)
              .select()
              .single();

            if (error) throw error;

            results.push(updatedProfile);

            // Create audit log
            await createAuditLog({
              user_id: userId,
              user_email: userEmail,
              action: 'ENABLE',
              resource_type: 'profile',
              resource_id: profileId,
              new_values: { disabled: false },
              ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
              user_agent: request.headers.get('user-agent')
            });
          } catch (err) {
            errors.push({ profileId, error: err instanceof Error ? err.message : 'Unknown error' });
          }
        }
        break;

      case 'bulk_update_role':
        if (!data?.role) {
          return NextResponse.json({ error: 'Role is required for bulk role update' }, { status: 400 });
        }

        for (const profileId of profileIds) {
          try {
            const { data: updatedProfile, error } = await supabase
              .from('profiles')
              .update({ role: data.role })
              .eq('id', profileId)
              .select()
              .single();

            if (error) throw error;

            results.push(updatedProfile);

            // Create audit log
            await createAuditLog({
              user_id: userId,
              user_email: userEmail,
              action: 'UPDATE',
              resource_type: 'profile',
              resource_id: profileId,
              new_values: { role: data.role },
              ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
              user_agent: request.headers.get('user-agent')
            });
          } catch (err) {
            errors.push({ profileId, error: err instanceof Error ? err.message : 'Unknown error' });
          }
        }
        break;

      case 'bulk_assign_company':
        if (!data?.companyId) {
          return NextResponse.json({ error: 'Company ID is required for bulk company assignment' }, { status: 400 });
        }

        for (const profileId of profileIds) {
          try {
            const { data: updatedProfile, error } = await supabase
              .from('profiles')
              .update({ company_id: data.companyId })
              .eq('id', profileId)
              .select()
              .single();

            if (error) throw error;

            results.push(updatedProfile);

            // Create audit log
            await createAuditLog({
              user_id: userId,
              user_email: userEmail,
              action: 'UPDATE',
              resource_type: 'profile',
              resource_id: profileId,
              new_values: { company_id: data.companyId },
              ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
              user_agent: request.headers.get('user-agent')
            });
          } catch (err) {
            errors.push({ profileId, error: err instanceof Error ? err.message : 'Unknown error' });
          }
        }
        break;

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    return NextResponse.json({
      success: results.length,
      errors: errors.length,
      results,
      errorDetails: errors,
      message: `Bulk operation completed. ${results.length} successful, ${errors.length} failed.`
    });

  } catch (error) {
    console.error('Enhanced profiles bulk operation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
