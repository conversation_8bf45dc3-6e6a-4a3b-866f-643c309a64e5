import NextAuth from "next-auth"
import Facebook from "next-auth/providers/facebook"
import GitHub from "next-auth/providers/github"
import Google from "next-auth/providers/google"
import { insertData, getDataValue } from "./src/lib/supabase"


export const { auth, handlers } = NextAuth({
  providers: [
    GitHub({
      clientId: process.env.GITHUB_CLIENT_ID ?? "",
      clientSecret: process.env.GITHUB_CLIENT_SECRET ?? "",
    }),
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID ?? "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET ?? "",
    }),
    Facebook({
      clientId: process.env.FACEBOOK_CLIENT_ID ?? "",
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET ?? "",
    }),
  ],
  secret: process.env.NEXT_AUTH_SECRET,
  pages: {
    signIn: '/auth/signin',
    error: '/auth/signin',
  },
  callbacks: {
    async signIn({ user }) {
      try {
        // Check if user already exists in profiles table
        if (user.email) {
          const existingData = await getDataValue('profiles', 'email', user.email, 'id');

          // If user doesn't exist, create profile entry
          if (existingData === null) {
            console.log('Creating new profile for user:', user.email);
            const { data, error } = await insertData('profiles', {
              full_name: user.name || '',
              email: user.email,
              avatar_url: user.image || '',
              role: 'user',
              is_company_admin: false,
              disabled: false,
              timezone: 'UTC'
            });

            if (error) {
              console.error('Error creating user profile in auth callback:', error);
              // Don't block login, the profile API will handle creation as fallback
            } else {
              console.log('User profile created successfully in auth callback:', data);
            }
          } else {
            // User exists, check if they are disabled
            const disabledStatus = await getDataValue('profiles', 'email', user.email, 'disabled');
            if (disabledStatus === true) {
              console.log('Login blocked for disabled user:', user.email);
              return false; // Block login for disabled users
            }
          }
        }
        return true;
      } catch (error) {
        console.error('Error in signIn callback:', error);
        return true; // Allow signin even if profile creation fails - API will handle it
      }
    },
  },
})
