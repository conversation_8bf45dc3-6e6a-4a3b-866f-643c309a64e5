'use client';

import { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  Text,
  Group,
  Button,
  Paper,
  Alert,
  LoadingOverlay,
  Modal,
} from '@mantine/core';
import {
  IconPlus,
  IconAlertCircle,
  IconCheck,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { DynamicFormBuilder } from 'src/components/DynamicForm/DynamicFormBuilder';
import { CoreField } from 'src/types/coreFields';

interface CustomField {
  id: string;
  name: string;
  field_key: string;
  field_type: string;
  description: string | null;
  is_required: boolean;
  is_active: boolean;
  default_value: string | null;
  validation_rules: Record<string, any>;
  dropdown_options: string[] | null;
  display_order: number;
}

interface UserProfile {
  id: string;
  company_id: string | null;
}

export function RecordCreation() {
  const [coreFields, setCoreFields] = useState<CoreField[]>([]);
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [modalOpen, setModalOpen] = useState(false);

  // Load form fields
  const loadFields = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/forms/fields');
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load form fields');
      }

      const data = await response.json();
      setCoreFields(data.coreFields);
      setCustomFields(data.customFields);
      setUserProfile(data.userProfile);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load form fields';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFields();
  }, []);

  // Handle form submission
  const handleSubmit = async (data: { coreFieldData: Record<string, any>; customFieldData: Record<string, any> }) => {
    try {
      const response = await fetch('/api/records', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit record');
      }

      const result = await response.json();
      
      notifications.show({
        title: 'Success',
        message: 'Record created successfully!',
        color: 'green',
        icon: <IconCheck size={16} />
      });

      setModalOpen(false);
      // Optionally refresh records list or redirect
    } catch (err) {
      throw err; // Re-throw to let DynamicFormBuilder handle the error display
    }
  };

  if (loading) {
    return (
      <div style={{ position: 'relative', minHeight: 400 }}>
        <LoadingOverlay visible />
      </div>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
        {error}
      </Alert>
    );
  }

  if (!userProfile?.company_id) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} title="No Company" color="yellow">
        You must be associated with a company to create records.
      </Alert>
    );
  }

  return (
    <Stack gap="md">
      {/* Header */}
      <Group justify="space-between">
        <div>
          <Title order={3}>Create New Record</Title>
          <Text c="dimmed">Add a new record to your company database</Text>
        </div>
        <Button
          leftSection={<IconPlus size={16} />}
          onClick={() => setModalOpen(true)}
        >
          Create Record
        </Button>
      </Group>

      {/* Quick Stats */}
      <Paper withBorder p="lg">
        <Group>
          <div>
            <Text size="sm" c="dimmed">Available Fields</Text>
            <Text size="xl" fw={700}>
              {coreFields.length + customFields.length}
            </Text>
          </div>
          <div>
            <Text size="sm" c="dimmed">Core Fields</Text>
            <Text size="xl" fw={700}>
              {coreFields.length}
            </Text>
          </div>
          <div>
            <Text size="sm" c="dimmed">Custom Fields</Text>
            <Text size="xl" fw={700}>
              {customFields.length}
            </Text>
          </div>
        </Group>
      </Paper>

      {/* Create Record Modal */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title="Create New Record"
        size="lg"
      >
        <DynamicFormBuilder
          coreFields={coreFields}
          customFields={customFields}
          onSubmit={handleSubmit}
          onCancel={() => setModalOpen(false)}
          title="New Record"
          description="Fill out the form below to create a new record in your company database."
          submitLabel="Create Record"
        />
      </Modal>

      {/* Help Text */}
      <Paper p="md" withBorder bg="gray.0">
        <Stack gap="xs">
          <Title order={4}>About Records</Title>
          <Text size="sm" c="dimmed">
            • Records contain both core fields (defined by system admin) and custom fields (defined by your company)
          </Text>
          <Text size="sm" c="dimmed">
            • All required fields must be completed before the record can be saved
          </Text>
          <Text size="sm" c="dimmed">
            • Records are private to your company and can only be viewed by team members
          </Text>
        </Stack>
      </Paper>
    </Stack>
  );
}
