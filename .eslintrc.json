{"extends": "next/core-web-vitals", "rules": {"@next/next/no-html-link-for-pages": "off", "@next/next/no-page-custom-font": "off", "@next/next/no-duplicate-head": "off", "@next/next/no-before-interactive-script-outside-document": "off", "@next/next/no-styled-jsx-in-document": "off", "@next/next/no-typos": "off", "@next/next/no-head-element": "off", "@next/next/next-script-for-ga": "off", "react/no-unescaped-entities": "off", "react-hooks/exhaustive-deps": "off", "react-hooks/rules-of-hooks": "off", "jsx-a11y/alt-text": "off"}}