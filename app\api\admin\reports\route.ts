import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { verifyAdminAuth } from 'src/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can access reports
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const reportType = searchParams.get('type') || 'records'; // records, companies, users, fields
    const companyId = searchParams.get('companyId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const format = searchParams.get('format') || 'json'; // json, csv
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '100');
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const filters = searchParams.get('filters'); // JSON string of filters

    const supabase = getSupabaseClient();
    const offset = (page - 1) * limit;

    let reportData: any[] = [];
    let totalCount = 0;
    let columns: string[] = [];

    switch (reportType) {
      case 'records':
        const recordsResult = await generateRecordsReport(
          supabase, 
          { companyId, startDate, endDate, sortBy, sortOrder, limit, offset, filters }
        );
        reportData = recordsResult.data;
        totalCount = recordsResult.count;
        columns = recordsResult.columns;
        break;

      case 'companies':
        const companiesResult = await generateCompaniesReport(
          supabase,
          { startDate, endDate, sortBy, sortOrder, limit, offset, filters }
        );
        reportData = companiesResult.data;
        totalCount = companiesResult.count;
        columns = companiesResult.columns;
        break;

      case 'users':
        const usersResult = await generateUsersReport(
          supabase,
          { companyId, startDate, endDate, sortBy, sortOrder, limit, offset, filters }
        );
        reportData = usersResult.data;
        totalCount = usersResult.count;
        columns = usersResult.columns;
        break;

      case 'fields':
        const fieldsResult = await generateFieldsReport(
          supabase,
          { companyId, startDate, endDate, sortBy, sortOrder, limit, offset, filters }
        );
        reportData = fieldsResult.data;
        totalCount = fieldsResult.count;
        columns = fieldsResult.columns;
        break;

      default:
        return NextResponse.json({ error: 'Invalid report type' }, { status: 400 });
    }

    if (format === 'csv') {
      const csv = generateCSV(reportData, columns);
      return new NextResponse(csv, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${reportType}-report-${new Date().toISOString().split('T')[0]}.csv"`
        }
      });
    }

    return NextResponse.json({
      data: reportData,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit)
      },
      columns,
      reportType,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Reports API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Generate records report
async function generateRecordsReport(supabase: any, options: any) {
  const { companyId, startDate, endDate, sortBy, sortOrder, limit, offset, filters } = options;
  
  let query = supabase
    .from('records')
    .select(`
      id,
      created_at,
      updated_at,
      company_id,
      status,
      core_field_data,
      custom_field_data,
      tags,
      companies:company_id(name, slug),
      created_by_profile:created_by(full_name, email),
      updated_by_profile:updated_by(full_name, email)
    `, { count: 'exact' });

  // Apply filters
  if (companyId) query = query.eq('company_id', companyId);
  if (startDate) query = query.gte('created_at', startDate);
  if (endDate) query = query.lte('created_at', endDate);
  
  if (filters) {
    try {
      const filterObj = JSON.parse(filters);
      Object.entries(filterObj).forEach(([key, value]) => {
        if (value) query = query.eq(key, value);
      });
    } catch (e) {
      // Invalid JSON filters, ignore
    }
  }

  // Apply sorting and pagination
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });
  const { data, error, count } = await query.range(offset, offset + limit - 1);

  if (error) throw error;

  // Flatten the data for reporting
  const flattenedData = (data || []).map((record: any) => ({
    id: record.id,
    created_at: record.created_at,
    updated_at: record.updated_at,
    company_name: record.companies?.name || 'Unknown',
    company_slug: record.companies?.slug || 'unknown',
    status: record.status,
    created_by: record.created_by_profile?.full_name || record.created_by_profile?.email || 'Unknown',
    updated_by: record.updated_by_profile?.full_name || record.updated_by_profile?.email || 'Unknown',
    tags: Array.isArray(record.tags) ? record.tags.join(', ') : '',
    ...flattenCoreFieldData(record.core_field_data),
    ...flattenCustomFieldData(record.custom_field_data)
  }));

  const columns = [
    'id', 'created_at', 'updated_at', 'company_name', 'company_slug', 
    'status', 'created_by', 'updated_by', 'tags',
    ...extractFieldColumns(data || [])
  ];

  return { data: flattenedData, count: count || 0, columns };
}

// Generate companies report
async function generateCompaniesReport(supabase: any, options: any) {
  const { startDate, endDate, sortBy, sortOrder, limit, offset, filters } = options;
  
  let query = supabase
    .from('companies')
    .select(`
      id,
      created_at,
      updated_at,
      name,
      slug,
      domain,
      subscription_plan,
      subscription_status,
      max_users,
      max_records,
      disabled,
      profiles:profiles(count),
      records:records(count)
    `, { count: 'exact' });

  // Apply filters
  if (startDate) query = query.gte('created_at', startDate);
  if (endDate) query = query.lte('created_at', endDate);
  
  if (filters) {
    try {
      const filterObj = JSON.parse(filters);
      Object.entries(filterObj).forEach(([key, value]) => {
        if (value) query = query.eq(key, value);
      });
    } catch (e) {
      // Invalid JSON filters, ignore
    }
  }

  // Apply sorting and pagination
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });
  const { data, error, count } = await query.range(offset, offset + limit - 1);

  if (error) throw error;

  // Flatten the data for reporting
  const flattenedData = (data || []).map((company: any) => ({
    id: company.id,
    created_at: company.created_at,
    updated_at: company.updated_at,
    name: company.name,
    slug: company.slug,
    domain: company.domain || '',
    subscription_plan: company.subscription_plan,
    subscription_status: company.subscription_status,
    max_users: company.max_users,
    max_records: company.max_records,
    current_users: company.profiles?.[0]?.count || 0,
    current_records: company.records?.[0]?.count || 0,
    disabled: company.disabled ? 'Yes' : 'No'
  }));

  const columns = [
    'id', 'created_at', 'updated_at', 'name', 'slug', 'domain',
    'subscription_plan', 'subscription_status', 'max_users', 'max_records',
    'current_users', 'current_records', 'disabled'
  ];

  return { data: flattenedData, count: count || 0, columns };
}

// Generate users report
async function generateUsersReport(supabase: any, options: any) {
  const { companyId, startDate, endDate, sortBy, sortOrder, limit, offset, filters } = options;
  
  let query = supabase
    .from('profiles')
    .select(`
      id,
      created_at,
      updated_at,
      full_name,
      email,
      company_id,
      role,
      is_company_admin,
      disabled,
      last_login_at,
      timezone,
      companies:company_id(name, slug)
    `, { count: 'exact' });

  // Apply filters
  if (companyId) query = query.eq('company_id', companyId);
  if (startDate) query = query.gte('created_at', startDate);
  if (endDate) query = query.lte('created_at', endDate);
  
  if (filters) {
    try {
      const filterObj = JSON.parse(filters);
      Object.entries(filterObj).forEach(([key, value]) => {
        if (value) query = query.eq(key, value);
      });
    } catch (e) {
      // Invalid JSON filters, ignore
    }
  }

  // Apply sorting and pagination
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });
  const { data, error, count } = await query.range(offset, offset + limit - 1);

  if (error) throw error;

  // Flatten the data for reporting
  const flattenedData = (data || []).map((profile: any) => ({
    id: profile.id,
    created_at: profile.created_at,
    updated_at: profile.updated_at,
    full_name: profile.full_name || '',
    email: profile.email || '',
    company_name: profile.companies?.name || 'No Company',
    company_slug: profile.companies?.slug || '',
    role: profile.role,
    is_company_admin: profile.is_company_admin ? 'Yes' : 'No',
    disabled: profile.disabled ? 'Yes' : 'No',
    last_login_at: profile.last_login_at || '',
    timezone: profile.timezone
  }));

  const columns = [
    'id', 'created_at', 'updated_at', 'full_name', 'email',
    'company_name', 'company_slug', 'role', 'is_company_admin',
    'disabled', 'last_login_at', 'timezone'
  ];

  return { data: flattenedData, count: count || 0, columns };
}

// Generate fields report
async function generateFieldsReport(supabase: any, options: any) {
  const { companyId, startDate, endDate, sortBy, sortOrder, limit, offset } = options;
  
  // Get core fields and custom fields
  const [coreFieldsResult, customFieldsResult] = await Promise.all([
    supabase
      .from('core_fields')
      .select('*')
      .order(sortBy, { ascending: sortOrder === 'asc' }),
    
    supabase
      .from('custom_fields')
      .select(`
        *,
        companies:company_id(name, slug)
      `)
      .then((result: any) => {
        if (companyId && result.data) {
          result.data = result.data.filter((field: any) => field.company_id === companyId);
        }
        return result;
      })
  ]);

  if (coreFieldsResult.error) throw coreFieldsResult.error;
  if (customFieldsResult.error) throw customFieldsResult.error;

  const coreFields = (coreFieldsResult.data || []).map((field: any) => ({
    ...field,
    field_scope: 'Core',
    company_name: 'All Companies',
    company_slug: 'all'
  }));

  const customFields = (customFieldsResult.data || []).map((field: any) => ({
    ...field,
    field_scope: 'Custom',
    company_name: field.companies?.name || 'Unknown',
    company_slug: field.companies?.slug || 'unknown'
  }));

  const allFields = [...coreFields, ...customFields];
  const totalCount = allFields.length;

  // Apply pagination
  const paginatedFields = allFields.slice(offset, offset + limit);

  const columns = [
    'id', 'created_at', 'updated_at', 'name', 'field_key', 'field_type',
    'field_scope', 'company_name', 'company_slug', 'description',
    'is_required', 'is_active', 'default_value', 'display_order'
  ];

  return { data: paginatedFields, count: totalCount, columns };
}

// Helper functions
function flattenCoreFieldData(coreFieldData: any): Record<string, any> {
  if (!coreFieldData || typeof coreFieldData !== 'object') return {};
  
  const flattened: Record<string, any> = {};
  Object.entries(coreFieldData).forEach(([key, value]) => {
    flattened[`core_${key}`] = value;
  });
  return flattened;
}

function flattenCustomFieldData(customFieldData: any): Record<string, any> {
  if (!customFieldData || typeof customFieldData !== 'object') return {};
  
  const flattened: Record<string, any> = {};
  Object.entries(customFieldData).forEach(([key, value]) => {
    flattened[`custom_${key}`] = value;
  });
  return flattened;
}

function extractFieldColumns(records: any[]): string[] {
  const fieldColumns = new Set<string>();
  
  records.forEach(record => {
    if (record.core_field_data) {
      Object.keys(record.core_field_data).forEach(key => {
        fieldColumns.add(`core_${key}`);
      });
    }
    if (record.custom_field_data) {
      Object.keys(record.custom_field_data).forEach(key => {
        fieldColumns.add(`custom_${key}`);
      });
    }
  });
  
  return Array.from(fieldColumns).sort();
}

function generateCSV(data: any[], columns: string[]): string {
  const headers = columns.join(',');
  const rows = data.map(row => 
    columns.map(col => {
      const value = row[col];
      if (value === null || value === undefined) return '';
      if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    }).join(',')
  );
  
  return [headers, ...rows].join('\n');
}
