'use client';

import { useState, useEffect } from 'react';
import {
  Container,
  Title,
  Text,
  Group,
  Button,
  TextInput,
  Select,
  Paper,
  Badge,
  Alert,
  LoadingOverlay,
  Pagination,
  Table,
  Stack,
  Card,
  Divider,
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconAlertCircle,
  IconEye,
  IconCalendar,
  IconUser,
} from '@tabler/icons-react';
import { useRouter } from 'next/navigation';

interface Record {
  id: string;
  created_at: string;
  updated_at: string;
  core_field_data: Record<string, any>;
  custom_field_data: Record<string, any>;
  status: string;
  tags: string[];
  metadata: Record<string, any>;
  created_by_profile: {
    full_name: string | null;
    email: string | null;
  } | null;
  updated_by_profile: {
    full_name: string | null;
    email: string | null;
  } | null;
}

interface RecordsFilters {
  search: string;
  status: string;
  page: number;
  limit: number;
}

export default function RecordsPage() {
  const router = useRouter();
  const [records, setRecords] = useState<Record[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<RecordsFilters>({
    search: '',
    status: 'active',
    page: 1,
    limit: 20
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // Load records
  const loadRecords = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value.toString());
      });

      const response = await fetch(`/api/records?${params.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load records');
      }

      const data = await response.json();
      setRecords(data.data);
      setPagination(data.pagination);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load records';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadRecords();
  }, [filters]);

  // Handle filter changes
  const handleFilterChange = (key: keyof RecordsFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // Format field data for display
  const formatFieldData = (data: Record<string, any>) => {
    return Object.entries(data)
      .filter(([_, value]) => value !== null && value !== undefined && value !== '')
      .map(([key, value]) => ({
        key: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        value: typeof value === 'boolean' ? (value ? 'Yes' : 'No') : value.toString()
      }));
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Container size="xl" py="xl">
      <Stack gap="xl">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Title order={1}>Records</Title>
            <Text c="dimmed">
              View and manage your submitted records
            </Text>
          </div>
          <Button 
            leftSection={<IconPlus size={16} />}
            onClick={() => router.push('/form')}
          >
            New Record
          </Button>
        </Group>

        {/* Filters */}
        <Paper p="md" withBorder>
          <Group>
            <TextInput
              placeholder="Search records..."
              leftSection={<IconSearch size={16} />}
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              style={{ flex: 1 }}
            />
            <Select
              placeholder="Status"
              data={[
                { value: 'active', label: 'Active' },
                { value: 'archived', label: 'Archived' },
                { value: 'deleted', label: 'Deleted' }
              ]}
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value || 'active')}
            />
          </Group>
        </Paper>

        {/* Error Alert */}
        {error && (
          <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
            {error}
          </Alert>
        )}

        {/* Records */}
        <div style={{ position: 'relative' }}>
          <LoadingOverlay visible={loading} />
          
          {records.length > 0 ? (
            <Stack gap="md">
              {records.map((record) => {
                const coreFieldEntries = formatFieldData(record.core_field_data);
                const customFieldEntries = formatFieldData(record.custom_field_data);
                
                return (
                  <Card key={record.id} withBorder padding="lg">
                    <Stack gap="md">
                      {/* Record Header */}
                      <Group justify="space-between">
                        <Group>
                          <Badge variant="light" color="blue">
                            Record #{record.id.substring(0, 8)}
                          </Badge>
                          <Badge 
                            variant="light" 
                            color={record.status === 'active' ? 'green' : 'gray'}
                          >
                            {record.status}
                          </Badge>
                          {record.tags.length > 0 && (
                            <Group gap="xs">
                              {record.tags.map((tag, index) => (
                                <Badge key={index} size="sm" variant="outline">
                                  {tag}
                                </Badge>
                              ))}
                            </Group>
                          )}
                        </Group>
                        <Group gap="xs" c="dimmed">
                          <IconCalendar size={14} />
                          <Text size="sm">{formatDate(record.created_at)}</Text>
                        </Group>
                      </Group>

                      {/* Core Fields */}
                      {coreFieldEntries.length > 0 && (
                        <div>
                          <Text fw={500} size="sm" mb="xs">Core Information</Text>
                          <Table>
                            <Table.Tbody>
                              {coreFieldEntries.map((entry, index) => (
                                <Table.Tr key={index}>
                                  <Table.Td w="30%" fw={500}>{entry.key}</Table.Td>
                                  <Table.Td>{entry.value}</Table.Td>
                                </Table.Tr>
                              ))}
                            </Table.Tbody>
                          </Table>
                        </div>
                      )}

                      {/* Custom Fields */}
                      {customFieldEntries.length > 0 && (
                        <div>
                          <Text fw={500} size="sm" mb="xs">Additional Information</Text>
                          <Table>
                            <Table.Tbody>
                              {customFieldEntries.map((entry, index) => (
                                <Table.Tr key={index}>
                                  <Table.Td w="30%" fw={500}>{entry.key}</Table.Td>
                                  <Table.Td>{entry.value}</Table.Td>
                                </Table.Tr>
                              ))}
                            </Table.Tbody>
                          </Table>
                        </div>
                      )}

                      {/* Record Footer */}
                      <Divider />
                      <Group justify="space-between" c="dimmed">
                        <Group gap="xs">
                          <IconUser size={14} />
                          <Text size="xs">
                            Created by {record.created_by_profile?.full_name || record.created_by_profile?.email || 'Unknown'}
                          </Text>
                        </Group>
                        {record.updated_at !== record.created_at && (
                          <Text size="xs">
                            Updated {formatDate(record.updated_at)}
                          </Text>
                        )}
                      </Group>
                    </Stack>
                  </Card>
                );
              })}
            </Stack>
          ) : !loading ? (
            <Paper p="xl" ta="center">
              <Text c="dimmed" size="lg">
                No records found
              </Text>
              <Text c="dimmed" size="sm" mt="xs">
                {filters.search ? 'Try adjusting your search criteria' : 'Create your first record to get started'}
              </Text>
              <Button 
                mt="md"
                leftSection={<IconPlus size={16} />}
                onClick={() => router.push('/form')}
              >
                Create New Record
              </Button>
            </Paper>
          ) : null}
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <Group justify="center">
            <Pagination
              value={pagination.page}
              onChange={handlePageChange}
              total={pagination.totalPages}
            />
          </Group>
        )}
      </Stack>
    </Container>
  );
}
