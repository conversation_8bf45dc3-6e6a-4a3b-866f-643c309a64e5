import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';

/**
 * GET /api/company/team
 * Get company team members
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, is_company_admin, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company' }, { status: 403 });
    }

    if (!profile.is_company_admin) {
      return NextResponse.json({ error: 'Company admin access required' }, { status: 403 });
    }

    // Get team members
    const { data: teamMembers, error: teamError } = await supabase
      .from('profiles')
      .select(`
        id,
        full_name,
        email,
        role,
        is_company_admin,
        disabled,
        last_login_at,
        created_at
      `)
      .eq('company_id', profile.company_id)
      .order('created_at', { ascending: false });

    if (teamError) {
      console.error('Error fetching team members:', teamError);
      return NextResponse.json({ error: 'Failed to fetch team members' }, { status: 500 });
    }

    return NextResponse.json({
      data: teamMembers || []
    });

  } catch (error) {
    console.error('Team GET API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
