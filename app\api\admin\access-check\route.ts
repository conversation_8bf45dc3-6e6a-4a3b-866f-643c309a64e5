import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from 'src/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    
    return NextResponse.json({
      isAuthorized: authResult.isAuthorized,
      isSuperAdmin: authResult.isSuperAdmin,
      error: authResult.error
    });
  } catch (error) {
    console.error('Admin access check API error:', error);
    return NextResponse.json({
      isAuthorized: false,
      isSuperAdmin: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
