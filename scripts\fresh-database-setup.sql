-- Fresh database setup script for ODude CRM Multi-Tenant SaaS Platform
-- This script creates the required tables for a comprehensive multi-tenant CRM system
--
-- Features included:
-- - Multi-tenant company management
-- - User profiles with company associations
-- - Core fields management (admin-defined fields for all companies)
-- - Custom fields management (company-specific fields)
-- - Dynamic records storage with core and custom field data
-- - Comprehensive audit logging
-- - User-specific settings and configurations
-- - Proper indexing for performance and multi-tenancy

-- =====================================================
-- COMPANIES & MULTI-TENANCY
-- =====================================================

-- Create companies table for multi-tenant architecture
CREATE TABLE IF NOT EXISTS companies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL, -- URL-friendly identifier
  domain TEXT, -- Optional custom domain
  logo_url TEXT,
  description TEXT,
  subscription_plan TEXT DEFAULT 'free', -- free, basic, premium, enterprise
  subscription_status TEXT DEFAULT 'active', -- active, suspended, cancelled
  max_users INTEGER DEFAULT 10,
  max_records INTEGER DEFAULT 1000,
  settings JSONB DEFAULT '{}', -- Company-specific settings
  disabled BOOLEAN DEFAULT FALSE
);

-- Add indexes for companies table
CREATE INDEX IF NOT EXISTS idx_companies_slug ON companies (slug);
CREATE INDEX IF NOT EXISTS idx_companies_domain ON companies (domain);
CREATE INDEX IF NOT EXISTS idx_companies_subscription_status ON companies (subscription_status);
CREATE INDEX IF NOT EXISTS idx_companies_created_at ON companies (created_at);

-- Add comments for companies table
COMMENT ON TABLE companies IS 'Companies/tenants in the multi-tenant system';
COMMENT ON COLUMN companies.id IS 'Unique identifier for the company';
COMMENT ON COLUMN companies.slug IS 'URL-friendly company identifier';
COMMENT ON COLUMN companies.domain IS 'Optional custom domain for the company';
COMMENT ON COLUMN companies.subscription_plan IS 'Company subscription tier';
COMMENT ON COLUMN companies.max_users IS 'Maximum users allowed for this company';
COMMENT ON COLUMN companies.max_records IS 'Maximum records allowed for this company';

-- =====================================================
-- USER PROFILES WITH COMPANY ASSOCIATIONS
-- =====================================================

-- Update profiles table to support multi-tenancy
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  full_name TEXT,
  avatar_url TEXT,
  email TEXT UNIQUE,
  disabled BOOLEAN DEFAULT FALSE,
  company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
  role TEXT DEFAULT 'user', -- admin, user, viewer
  is_company_admin BOOLEAN DEFAULT FALSE,
  last_login_at TIMESTAMPTZ,
  timezone TEXT DEFAULT 'UTC'
);

-- Add indexes for profiles table
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles (email);
CREATE INDEX IF NOT EXISTS idx_profiles_company_id ON profiles (company_id);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles (role);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON profiles (created_at);
CREATE INDEX IF NOT EXISTS idx_profiles_company_admin ON profiles (company_id, is_company_admin);

-- Add comments for profiles table
COMMENT ON TABLE profiles IS 'User profiles with company associations';
COMMENT ON COLUMN profiles.id IS 'Unique identifier for the profile';
COMMENT ON COLUMN profiles.email IS 'User email address (unique)';
COMMENT ON COLUMN profiles.full_name IS 'User full name';
COMMENT ON COLUMN profiles.avatar_url IS 'URL to user avatar image';
COMMENT ON COLUMN profiles.disabled IS 'Whether the user account is disabled';
COMMENT ON COLUMN profiles.company_id IS 'Associated company for multi-tenancy';
COMMENT ON COLUMN profiles.role IS 'User role within the system';
COMMENT ON COLUMN profiles.is_company_admin IS 'Whether user is admin of their company';
COMMENT ON COLUMN profiles.created_at IS 'When the profile was created';
COMMENT ON COLUMN profiles.updated_at IS 'When the profile was last updated';

-- =====================================================
-- CORE FIELDS MANAGEMENT (ADMIN-DEFINED)
-- =====================================================

-- Core fields that apply to all companies (managed by super admin)
CREATE TABLE IF NOT EXISTS core_fields (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  name TEXT NOT NULL, -- Field name (e.g., "Customer Name", "Order Status")
  field_key TEXT UNIQUE NOT NULL, -- Unique key for programmatic access (e.g., "customer_name", "order_status")
  field_type TEXT NOT NULL CHECK (field_type IN ('text', 'number', 'dropdown', 'checkbox', 'date', 'email', 'phone', 'price')),
  description TEXT,
  is_required BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  default_value TEXT, -- Default value for the field
  validation_rules JSONB DEFAULT '{}', -- JSON object with validation rules
  dropdown_options JSONB, -- For dropdown fields, array of options
  display_order INTEGER DEFAULT 0,
  created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  updated_by UUID REFERENCES profiles(id) ON DELETE SET NULL
);

-- Add indexes for core_fields table
CREATE INDEX IF NOT EXISTS idx_core_fields_field_key ON core_fields (field_key);
CREATE INDEX IF NOT EXISTS idx_core_fields_field_type ON core_fields (field_type);
CREATE INDEX IF NOT EXISTS idx_core_fields_is_active ON core_fields (is_active);
CREATE INDEX IF NOT EXISTS idx_core_fields_display_order ON core_fields (display_order);
CREATE INDEX IF NOT EXISTS idx_core_fields_created_at ON core_fields (created_at);

-- Add comments for core_fields table
COMMENT ON TABLE core_fields IS 'Core fields defined by admin that apply to all companies';
COMMENT ON COLUMN core_fields.field_key IS 'Unique programmatic identifier for the field';
COMMENT ON COLUMN core_fields.field_type IS 'Type of field (text, number, dropdown, etc.)';
COMMENT ON COLUMN core_fields.validation_rules IS 'JSON object containing validation rules';
COMMENT ON COLUMN core_fields.dropdown_options IS 'Array of options for dropdown fields';

-- =====================================================
-- CUSTOM FIELDS MANAGEMENT (COMPANY-SPECIFIC)
-- =====================================================

-- Custom fields that companies can create for themselves
CREATE TABLE IF NOT EXISTS custom_fields (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  name TEXT NOT NULL, -- Field name
  field_key TEXT NOT NULL, -- Key unique within company
  field_type TEXT NOT NULL CHECK (field_type IN ('text', 'number', 'dropdown', 'checkbox', 'date', 'email', 'phone', 'price')),
  description TEXT,
  is_required BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  default_value TEXT,
  validation_rules JSONB DEFAULT '{}',
  dropdown_options JSONB,
  display_order INTEGER DEFAULT 0,
  created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  updated_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  UNIQUE(company_id, field_key) -- Field key must be unique within company
);

-- Add indexes for custom_fields table
CREATE INDEX IF NOT EXISTS idx_custom_fields_company_id ON custom_fields (company_id);
CREATE INDEX IF NOT EXISTS idx_custom_fields_field_key ON custom_fields (company_id, field_key);
CREATE INDEX IF NOT EXISTS idx_custom_fields_field_type ON custom_fields (field_type);
CREATE INDEX IF NOT EXISTS idx_custom_fields_is_active ON custom_fields (company_id, is_active);
CREATE INDEX IF NOT EXISTS idx_custom_fields_display_order ON custom_fields (company_id, display_order);

-- Add comments for custom_fields table
COMMENT ON TABLE custom_fields IS 'Custom fields that companies can create for their specific needs';
COMMENT ON COLUMN custom_fields.company_id IS 'Company that owns this custom field';
COMMENT ON COLUMN custom_fields.field_key IS 'Field identifier unique within the company';

-- =====================================================
-- RECORDS STORAGE (DYNAMIC DATA)
-- =====================================================

-- Records table to store actual data submitted by users
CREATE TABLE IF NOT EXISTS records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  updated_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  core_field_data JSONB DEFAULT '{}', -- Data for core fields
  custom_field_data JSONB DEFAULT '{}', -- Data for custom fields
  status TEXT DEFAULT 'active', -- active, archived, deleted
  tags TEXT[], -- Array of tags for categorization
  metadata JSONB DEFAULT '{}' -- Additional metadata
);

-- Add indexes for records table
CREATE INDEX IF NOT EXISTS idx_records_company_id ON records (company_id);
CREATE INDEX IF NOT EXISTS idx_records_created_by ON records (created_by);
CREATE INDEX IF NOT EXISTS idx_records_status ON records (company_id, status);
CREATE INDEX IF NOT EXISTS idx_records_created_at ON records (company_id, created_at);
CREATE INDEX IF NOT EXISTS idx_records_core_field_data ON records USING GIN (core_field_data);
CREATE INDEX IF NOT EXISTS idx_records_custom_field_data ON records USING GIN (custom_field_data);
CREATE INDEX IF NOT EXISTS idx_records_tags ON records USING GIN (tags);

-- Add comments for records table
COMMENT ON TABLE records IS 'Dynamic records containing core and custom field data';
COMMENT ON COLUMN records.company_id IS 'Company that owns this record';
COMMENT ON COLUMN records.core_field_data IS 'JSON object containing core field values';
COMMENT ON COLUMN records.custom_field_data IS 'JSON object containing custom field values';
COMMENT ON COLUMN records.tags IS 'Array of tags for record categorization';

-- Create settings table for user-specific configurations
CREATE TABLE IF NOT EXISTS settings (
  email TEXT PRIMARY KEY,
  max_contact_limit INTEGER DEFAULT 4,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add index for settings table
CREATE INDEX IF NOT EXISTS idx_settings_email ON settings (email);

-- Add comments for settings table
COMMENT ON TABLE settings IS 'User-specific settings and configurations';
COMMENT ON COLUMN settings.email IS 'User email address (primary key)';
COMMENT ON COLUMN settings.max_contact_limit IS 'Maximum number of contacts allowed for this user';
COMMENT ON COLUMN settings.created_at IS 'When the settings record was created';
COMMENT ON COLUMN settings.updated_at IS 'When the settings record was last updated';

-- =====================================================
-- COMPANY INVITATIONS
-- =====================================================

-- Company invitations table for team management
CREATE TABLE IF NOT EXISTS company_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  invited_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  role TEXT DEFAULT 'user', -- user, admin, viewer
  is_company_admin BOOLEAN DEFAULT FALSE,
  status TEXT DEFAULT 'pending', -- pending, accepted, declined, expired
  token TEXT UNIQUE NOT NULL, -- Unique invitation token
  expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days'),
  accepted_at TIMESTAMPTZ,
  accepted_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  metadata JSONB DEFAULT '{}' -- Additional invitation metadata
);

-- Add unique constraint to prevent duplicate invitations
CREATE UNIQUE INDEX IF NOT EXISTS idx_company_invitations_unique_pending
ON company_invitations(company_id, email)
WHERE status = 'pending';

-- Add indexes for company_invitations table
CREATE INDEX IF NOT EXISTS idx_company_invitations_company_id ON company_invitations(company_id);
CREATE INDEX IF NOT EXISTS idx_company_invitations_email ON company_invitations(email);
CREATE INDEX IF NOT EXISTS idx_company_invitations_status ON company_invitations(status);
CREATE INDEX IF NOT EXISTS idx_company_invitations_token ON company_invitations(token);
CREATE INDEX IF NOT EXISTS idx_company_invitations_expires_at ON company_invitations(expires_at);
CREATE INDEX IF NOT EXISTS idx_company_invitations_invited_by ON company_invitations(invited_by);

-- Add comments for company_invitations table
COMMENT ON TABLE company_invitations IS 'Company invitations for team management';
COMMENT ON COLUMN company_invitations.company_id IS 'Company extending the invitation';
COMMENT ON COLUMN company_invitations.invited_by IS 'User who sent the invitation';
COMMENT ON COLUMN company_invitations.email IS 'Email address of invited user';
COMMENT ON COLUMN company_invitations.token IS 'Unique invitation token for acceptance';
COMMENT ON COLUMN company_invitations.expires_at IS 'When the invitation expires';
COMMENT ON COLUMN company_invitations.status IS 'Invitation status (pending, accepted, declined, expired)';

-- =====================================================
-- AUDIT LOGGING
-- =====================================================

-- Audit log table for tracking all admin actions
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  user_email TEXT NOT NULL, -- Store email for reference even if user is deleted
  company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
  action TEXT NOT NULL, -- CREATE, UPDATE, DELETE, LOGIN, etc.
  resource_type TEXT NOT NULL, -- core_field, custom_field, record, company, profile, etc.
  resource_id UUID, -- ID of the affected resource
  old_values JSONB, -- Previous values (for updates/deletes)
  new_values JSONB, -- New values (for creates/updates)
  ip_address INET,
  user_agent TEXT,
  metadata JSONB DEFAULT '{}'
);

-- Add indexes for audit_logs table
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs (user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_company_id ON audit_logs (company_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs (action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type ON audit_logs (resource_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_id ON audit_logs (resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs (created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_email ON audit_logs (user_email);

-- Add comments for audit_logs table
COMMENT ON TABLE audit_logs IS 'Comprehensive audit log for all system actions';
COMMENT ON COLUMN audit_logs.action IS 'Type of action performed (CREATE, UPDATE, DELETE, etc.)';
COMMENT ON COLUMN audit_logs.resource_type IS 'Type of resource affected';
COMMENT ON COLUMN audit_logs.old_values IS 'Previous values before the change';
COMMENT ON COLUMN audit_logs.new_values IS 'New values after the change';

-- =====================================================
-- TRIGGER FUNCTIONS FOR AUTOMATIC TIMESTAMPS
-- =====================================================

-- Generic function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Legacy functions for backward compatibility
CREATE OR REPLACE FUNCTION update_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CREATE TRIGGERS FOR AUTOMATIC TIMESTAMPS
-- =====================================================

-- Triggers for companies table
CREATE TRIGGER trigger_update_companies_updated_at
    BEFORE UPDATE ON companies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Triggers for profiles table
CREATE TRIGGER trigger_update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_profiles_updated_at();

-- Triggers for core_fields table
CREATE TRIGGER trigger_update_core_fields_updated_at
    BEFORE UPDATE ON core_fields
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Triggers for custom_fields table
CREATE TRIGGER trigger_update_custom_fields_updated_at
    BEFORE UPDATE ON custom_fields
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Triggers for records table
CREATE TRIGGER trigger_update_records_updated_at
    BEFORE UPDATE ON records
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Triggers for settings table
CREATE TRIGGER trigger_update_settings_updated_at
    BEFORE UPDATE ON settings
    FOR EACH ROW
    EXECUTE FUNCTION update_settings_updated_at();

-- =====================================================
-- INSERT SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert sample core fields (these would be created by super admin)
INSERT INTO core_fields (name, field_key, field_type, description, is_required, is_active, display_order) VALUES
('Customer Name', 'customer_name', 'text', 'Full name of the customer', true, true, 1),
('Email Address', 'email', 'email', 'Customer email address', true, true, 2),
('Phone Number', 'phone', 'phone', 'Customer phone number', false, true, 3),
('Order Status', 'order_status', 'dropdown', 'Current status of the order', true, true, 4),
('Order Value', 'order_value', 'price', 'Total value of the order', false, true, 5),
('Order Date', 'order_date', 'date', 'Date when order was placed', true, true, 6),
('Payment Method', 'payment_method', 'dropdown', 'Method of payment', true, true, 7),
('Is Priority', 'is_priority', 'checkbox', 'Whether this is a priority order', false, true, 8)
ON CONFLICT (field_key) DO NOTHING;

-- Update dropdown options for relevant fields
UPDATE core_fields SET dropdown_options = '["Pending", "Processing", "Shipped", "Delivered", "Cancelled"]'::jsonb
WHERE field_key = 'order_status';

UPDATE core_fields SET dropdown_options = '["Cash on Delivery", "Advance Paid", "Credit Card", "Bank Transfer", "UPI"]'::jsonb
WHERE field_key = 'payment_method';

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verification queries
SELECT 'Multi-tenant CRM database setup completed successfully!' as status;
SELECT 'Companies: ' || COUNT(*) as companies_count FROM companies;
SELECT 'Profiles: ' || COUNT(*) as profiles_count FROM profiles;
SELECT 'Core Fields: ' || COUNT(*) as core_fields_count FROM core_fields;
SELECT 'Custom Fields: ' || COUNT(*) as custom_fields_count FROM custom_fields;
SELECT 'Records: ' || COUNT(*) as records_count FROM records;
SELECT 'Settings: ' || COUNT(*) as settings_count FROM settings;
SELECT 'Audit Logs: ' || COUNT(*) as audit_logs_count FROM audit_logs;

-- Show sample core fields
SELECT 'Sample Core Fields:' as info;
SELECT field_key, name, field_type, is_required, is_active FROM core_fields ORDER BY display_order;
