import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { createAuditLog } from 'src/lib/auditLog';
import { auth } from 'auth';

export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company to create records' }, { status: 403 });
    }

    const body = await request.json();
    const { coreFieldData = {}, customFieldData = {}, tags = [], metadata = {} } = body;

    // Validate that required core fields are provided
    const { data: coreFields, error: coreFieldsError } = await supabase
      .from('core_fields')
      .select('field_key, name, is_required, is_active')
      .eq('is_active', true)
      .eq('is_required', true);

    if (coreFieldsError) {
      console.error('Error fetching core fields for validation:', coreFieldsError);
      return NextResponse.json({ error: 'Failed to validate fields' }, { status: 500 });
    }

    // Check required core fields
    const missingCoreFields: string[] = [];
    (coreFields || []).forEach(field => {
      const value = coreFieldData[field.field_key];
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        missingCoreFields.push(field.name);
      }
    });

    if (missingCoreFields.length > 0) {
      return NextResponse.json({ 
        error: `Missing required fields: ${missingCoreFields.join(', ')}` 
      }, { status: 400 });
    }

    // Validate required custom fields
    const { data: customFields, error: customFieldsError } = await supabase
      .from('custom_fields')
      .select('field_key, name, is_required, is_active')
      .eq('company_id', profile.company_id)
      .eq('is_active', true)
      .eq('is_required', true);

    if (customFieldsError) {
      console.error('Error fetching custom fields for validation:', customFieldsError);
      return NextResponse.json({ error: 'Failed to validate custom fields' }, { status: 500 });
    }

    // Check required custom fields
    const missingCustomFields: string[] = [];
    (customFields || []).forEach(field => {
      const value = customFieldData[field.field_key];
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        missingCustomFields.push(field.name);
      }
    });

    if (missingCustomFields.length > 0) {
      return NextResponse.json({ 
        error: `Missing required custom fields: ${missingCustomFields.join(', ')}` 
      }, { status: 400 });
    }

    // Check company record limits
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('max_records, subscription_status, disabled')
      .eq('id', profile.company_id)
      .single();

    if (companyError || !company) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 });
    }

    if (company.disabled) {
      return NextResponse.json({ error: 'Company account is disabled' }, { status: 403 });
    }

    if (company.subscription_status !== 'active') {
      return NextResponse.json({ error: 'Company subscription is not active' }, { status: 403 });
    }

    // Check current record count
    const { count: currentRecords } = await supabase
      .from('records')
      .select('*', { count: 'exact', head: true })
      .eq('company_id', profile.company_id)
      .eq('status', 'active');

    if (currentRecords && currentRecords >= company.max_records) {
      return NextResponse.json({ 
        error: `Record limit reached. Maximum ${company.max_records} records allowed for your subscription plan.` 
      }, { status: 403 });
    }

    // Create the record
    const { data: newRecord, error: createError } = await supabase
      .from('records')
      .insert({
        company_id: profile.company_id,
        created_by: profile.id,
        updated_by: profile.id,
        core_field_data: coreFieldData,
        custom_field_data: customFieldData,
        status: 'active',
        tags: Array.isArray(tags) ? tags : [],
        metadata
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating record:', createError);
      return NextResponse.json({ error: 'Failed to create record' }, { status: 500 });
    }

    // Create audit log
    await createAuditLog({
      user_id: profile.id,
      user_email: userEmail,
      company_id: profile.company_id,
      action: 'CREATE',
      resource_type: 'record',
      resource_id: newRecord.id,
      new_values: newRecord,
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      user_agent: request.headers.get('user-agent')
    });

    return NextResponse.json({ 
      data: newRecord,
      message: 'Record created successfully' 
    }, { status: 201 });

  } catch (error) {
    console.error('Records POST API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'active';
    const offset = (page - 1) * limit;

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company' }, { status: 403 });
    }

    // Build query
    let query = supabase
      .from('records')
      .select(`
        id,
        created_at,
        updated_at,
        core_field_data,
        custom_field_data,
        status,
        tags,
        metadata,
        created_by_profile:created_by(full_name, email),
        updated_by_profile:updated_by(full_name, email)
      `, { count: 'exact' })
      .eq('company_id', profile.company_id)
      .eq('status', status)
      .order('created_at', { ascending: false });

    // Apply search filter
    if (search) {
      // This is a simplified search - in production you might want more sophisticated search
      query = query.or(`core_field_data::text.ilike.%${search}%,custom_field_data::text.ilike.%${search}%`);
    }

    // Get total count for pagination
    const { count } = await supabase
      .from('records')
      .select('*', { count: 'exact', head: true })
      .eq('company_id', profile.company_id)
      .eq('status', status);

    // Get paginated results
    const { data: records, error: recordsError } = await query
      .range(offset, offset + limit - 1);

    if (recordsError) {
      console.error('Error fetching records:', recordsError);
      return NextResponse.json({ error: 'Failed to fetch records' }, { status: 500 });
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: records || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages
      }
    });

  } catch (error) {
    console.error('Records GET API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
