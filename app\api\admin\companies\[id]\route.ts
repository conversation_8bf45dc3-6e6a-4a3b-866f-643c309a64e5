import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { verifyAdminAuth } from 'src/lib/adminAuth';
import { createAuditLog } from 'src/lib/auditLog';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can access company details
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const supabase = getSupabaseClient();
    const { id } = params;

    // Get company details
    const { data: company, error } = await supabase
      .from('companies')
      .select(`
        id,
        created_at,
        updated_at,
        name,
        slug,
        domain,
        logo_url,
        description,
        subscription_plan,
        subscription_status,
        max_users,
        max_records,
        settings,
        disabled
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Company not found' }, { status: 404 });
      }
      console.error('Error fetching company:', error);
      return NextResponse.json({ error: 'Failed to fetch company' }, { status: 500 });
    }

    // Get user count
    const { count: userCount } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .eq('company_id', id);

    // Get record count
    const { count: recordCount } = await supabase
      .from('records')
      .select('*', { count: 'exact', head: true })
      .eq('company_id', id)
      .eq('status', 'active');

    // Get recent users
    const { data: recentUsers } = await supabase
      .from('profiles')
      .select('id, full_name, email, created_at, last_login_at, is_company_admin')
      .eq('company_id', id)
      .order('created_at', { ascending: false })
      .limit(5);

    const enrichedCompany = {
      ...company,
      user_count: userCount || 0,
      record_count: recordCount || 0,
      recent_users: recentUsers || []
    };

    return NextResponse.json({ data: enrichedCompany });

  } catch (error) {
    console.error('Company GET API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can update companies
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const supabase = getSupabaseClient();
    const { id } = params;
    const body = await request.json();

    // Get existing company for audit log
    const { data: existingCompany, error: fetchError } = await supabase
      .from('companies')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Company not found' }, { status: 404 });
      }
      console.error('Error fetching existing company:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch company' }, { status: 500 });
    }

    const {
      name,
      slug,
      domain,
      logo_url,
      description,
      subscription_plan,
      subscription_status,
      max_users,
      max_records,
      settings,
      disabled
    } = body;

    // Validation
    if (name !== undefined && !name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    if (slug !== undefined) {
      if (!slug) {
        return NextResponse.json({ error: 'Slug is required' }, { status: 400 });
      }
      if (!/^[a-z0-9-]+$/.test(slug)) {
        return NextResponse.json({ 
          error: 'Slug must contain only lowercase letters, numbers, and hyphens' 
        }, { status: 400 });
      }
      
      // Check if slug already exists (excluding current company)
      const { data: existingSlug } = await supabase
        .from('companies')
        .select('id')
        .eq('slug', slug)
        .neq('id', id)
        .single();

      if (existingSlug) {
        return NextResponse.json({ 
          error: 'Company slug already exists' 
        }, { status: 409 });
      }
    }

    // Check domain uniqueness (if provided and different from current)
    if (domain !== undefined && domain !== existingCompany.domain) {
      const { data: existingDomain } = await supabase
        .from('companies')
        .select('id')
        .eq('domain', domain)
        .neq('id', id)
        .single();

      if (existingDomain) {
        return NextResponse.json({ 
          error: 'Domain already exists' 
        }, { status: 409 });
      }
    }

    // Validate subscription plan
    if (subscription_plan !== undefined) {
      const validPlans = ['free', 'basic', 'premium', 'enterprise'];
      if (!validPlans.includes(subscription_plan)) {
        return NextResponse.json({ 
          error: `Invalid subscription plan. Valid plans: ${validPlans.join(', ')}` 
        }, { status: 400 });
      }
    }

    // Validate subscription status
    if (subscription_status !== undefined) {
      const validStatuses = ['active', 'suspended', 'cancelled'];
      if (!validStatuses.includes(subscription_status)) {
        return NextResponse.json({ 
          error: `Invalid subscription status. Valid statuses: ${validStatuses.join(', ')}` 
        }, { status: 400 });
      }
    }

    const userId = authResult.session?.user?.id;

    // Prepare update data
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (slug !== undefined) updateData.slug = slug;
    if (domain !== undefined) updateData.domain = domain;
    if (logo_url !== undefined) updateData.logo_url = logo_url;
    if (description !== undefined) updateData.description = description;
    if (subscription_plan !== undefined) updateData.subscription_plan = subscription_plan;
    if (subscription_status !== undefined) updateData.subscription_status = subscription_status;
    if (max_users !== undefined) updateData.max_users = max_users;
    if (max_records !== undefined) updateData.max_records = max_records;
    if (settings !== undefined) updateData.settings = settings;
    if (disabled !== undefined) updateData.disabled = disabled;

    // Update the company
    const { data: updatedCompany, error: updateError } = await supabase
      .from('companies')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating company:', updateError);
      return NextResponse.json({ error: 'Failed to update company' }, { status: 500 });
    }

    // Create audit log
    await createAuditLog({
      user_id: userId,
      user_email: authResult.session?.user?.email || '',
      action: 'UPDATE',
      resource_type: 'company',
      resource_id: id,
      old_values: existingCompany,
      new_values: updatedCompany,
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      user_agent: request.headers.get('user-agent')
    });

    return NextResponse.json({ 
      data: updatedCompany,
      message: 'Company updated successfully' 
    });

  } catch (error) {
    console.error('Company PUT API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can delete companies
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const supabase = getSupabaseClient();
    const { id } = params;

    // Get existing company for audit log
    const { data: existingCompany, error: fetchError } = await supabase
      .from('companies')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Company not found' }, { status: 404 });
      }
      console.error('Error fetching existing company:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch company' }, { status: 500 });
    }

    // Check if company has users or records
    const { count: userCount } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .eq('company_id', id);

    const { count: recordCount } = await supabase
      .from('records')
      .select('*', { count: 'exact', head: true })
      .eq('company_id', id);

    if ((userCount && userCount > 0) || (recordCount && recordCount > 0)) {
      return NextResponse.json({ 
        error: `Cannot delete company '${existingCompany.name}' as it has ${userCount || 0} user(s) and ${recordCount || 0} record(s). Consider disabling it instead.` 
      }, { status: 409 });
    }

    // Delete the company
    const { error: deleteError } = await supabase
      .from('companies')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting company:', deleteError);
      return NextResponse.json({ error: 'Failed to delete company' }, { status: 500 });
    }

    const userId = authResult.session?.user?.id;

    // Create audit log
    await createAuditLog({
      user_id: userId,
      user_email: authResult.session?.user?.email || '',
      action: 'DELETE',
      resource_type: 'company',
      resource_id: id,
      old_values: existingCompany,
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      user_agent: request.headers.get('user-agent')
    });

    return NextResponse.json({ 
      message: 'Company deleted successfully' 
    });

  } catch (error) {
    console.error('Company DELETE API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
