import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { verifyAdminAuth } from 'src/lib/adminAuth';
import { createAuditLog } from 'src/lib/auditLog';

// Field types that are supported
const SUPPORTED_FIELD_TYPES = ['text', 'number', 'dropdown', 'checkbox', 'date', 'email', 'phone', 'price'];

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can manage core fields
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';
    const fieldType = searchParams.get('fieldType') || '';
    const isActive = searchParams.get('isActive');
    const offset = (page - 1) * limit;

    const supabase = getSupabaseClient();

    // Build query
    let query = supabase
      .from('core_fields')
      .select(`
        id,
        created_at,
        updated_at,
        name,
        field_key,
        field_type,
        description,
        is_required,
        is_active,
        default_value,
        validation_rules,
        dropdown_options,
        display_order,
        created_by,
        updated_by
      `)
      .order('display_order', { ascending: true })
      .order('created_at', { ascending: false });

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,field_key.ilike.%${search}%,description.ilike.%${search}%`);
    }

    if (fieldType) {
      query = query.eq('field_type', fieldType);
    }

    if (isActive !== null && isActive !== '') {
      query = query.eq('is_active', isActive === 'true');
    }

    // Get total count for pagination
    const { count } = await supabase
      .from('core_fields')
      .select('*', { count: 'exact', head: true });

    // Get paginated results
    const { data: coreFields, error: coreFieldsError } = await query
      .range(offset, offset + limit - 1);

    if (coreFieldsError) {
      console.error('Error fetching core fields:', coreFieldsError);
      return NextResponse.json({ error: 'Failed to fetch core fields' }, { status: 500 });
    }

    // Get creator/updater information
    const userIds = [...new Set([
      ...coreFields.map(field => field.created_by).filter(Boolean),
      ...coreFields.map(field => field.updated_by).filter(Boolean)
    ])];

    let users = [];
    if (userIds.length > 0) {
      const { data: usersData } = await supabase
        .from('profiles')
        .select('id, full_name, email')
        .in('id', userIds);
      users = usersData || [];
    }

    // Enrich core fields with user information
    const enrichedCoreFields = coreFields.map(field => ({
      ...field,
      created_by_user: users.find(user => user.id === field.created_by),
      updated_by_user: users.find(user => user.id === field.updated_by)
    }));

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: enrichedCoreFields,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages
      },
      fieldTypes: SUPPORTED_FIELD_TYPES
    });

  } catch (error) {
    console.error('Core fields API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can create core fields
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const {
      name,
      field_key,
      field_type,
      description,
      is_required = false,
      is_active = true,
      default_value,
      validation_rules = {},
      dropdown_options,
      display_order = 0
    } = body;

    // Validation
    if (!name || !field_key || !field_type) {
      return NextResponse.json({ 
        error: 'Missing required fields: name, field_key, field_type' 
      }, { status: 400 });
    }

    if (!SUPPORTED_FIELD_TYPES.includes(field_type)) {
      return NextResponse.json({ 
        error: `Unsupported field type. Supported types: ${SUPPORTED_FIELD_TYPES.join(', ')}` 
      }, { status: 400 });
    }

    // Validate field_key format (alphanumeric and underscores only)
    if (!/^[a-z0-9_]+$/.test(field_key)) {
      return NextResponse.json({ 
        error: 'Field key must contain only lowercase letters, numbers, and underscores' 
      }, { status: 400 });
    }

    // For dropdown fields, validate that options are provided
    if (field_type === 'dropdown' && (!dropdown_options || !Array.isArray(dropdown_options) || dropdown_options.length === 0)) {
      return NextResponse.json({ 
        error: 'Dropdown fields must have at least one option' 
      }, { status: 400 });
    }

    const supabase = getSupabaseClient();
    const userId = authResult.session?.user?.id;

    // Check if field_key already exists
    const { data: existingField } = await supabase
      .from('core_fields')
      .select('id')
      .eq('field_key', field_key)
      .single();

    if (existingField) {
      return NextResponse.json({ 
        error: 'Field key already exists' 
      }, { status: 409 });
    }

    // Create the core field
    const { data: newField, error: createError } = await supabase
      .from('core_fields')
      .insert({
        name,
        field_key,
        field_type,
        description,
        is_required,
        is_active,
        default_value,
        validation_rules,
        dropdown_options,
        display_order,
        created_by: userId,
        updated_by: userId
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating core field:', createError);
      return NextResponse.json({ error: 'Failed to create core field' }, { status: 500 });
    }

    // Create audit log
    await createAuditLog({
      user_id: userId,
      user_email: authResult.session?.user?.email || '',
      action: 'CREATE',
      resource_type: 'core_field',
      resource_id: newField.id,
      new_values: newField,
      ip_address: request.ip,
      user_agent: request.headers.get('user-agent')
    });

    return NextResponse.json({ 
      data: newField,
      message: 'Core field created successfully' 
    }, { status: 201 });

  } catch (error) {
    console.error('Core fields POST API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
