'use client';

import { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  Text,
  Group,
  Button,
  Table,
  Badge,
  ActionIcon,
  Menu,
  Paper,
  Alert,
  LoadingOverlay,
  TextInput,
  Select,
  Pagination,
  Modal,
  ScrollArea,
} from '@mantine/core';
import { useDebouncedValue } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconPlus,
  IconDots,
  IconEdit,
  IconTrash,
  IconEye,
  IconSearch,
  IconDownload,
  IconAlertCircle,
  IconCheck,
} from '@tabler/icons-react';
import { RecordCreation } from './RecordCreation';

interface Record {
  id: string;
  created_at: string;
  updated_at: string | null;
  core_field_data: Record<string, any>;
  custom_field_data: Record<string, any>;
  status: string;
  tags: string[];
  metadata: Record<string, any>;
  created_by_profile?: {
    full_name: string | null;
    email: string | null;
  };
  updated_by_profile?: {
    full_name: string | null;
    email: string | null;
  };
}

interface RecordsResponse {
  data: Record[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export function RecordManagement() {
  const [records, setRecords] = useState<Record[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<Record | null>(null);

  const [debouncedSearch] = useDebouncedValue(searchQuery, 500);

  const loadRecords = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
      });

      if (debouncedSearch) {
        params.append('search', debouncedSearch);
      }

      if (statusFilter) {
        params.append('status', statusFilter);
      }

      const response = await fetch(`/api/records?${params}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load records');
      }

      const data: RecordsResponse = await response.json();
      setRecords(data.data || []);
      setTotalPages(data.pagination.totalPages);
      setTotalRecords(data.pagination.total);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load records';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadRecords();
  }, [currentPage, debouncedSearch, statusFilter]);

  const handleDelete = async (record: Record) => {
    if (!confirm(`Are you sure you want to delete this record? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/records/${record.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete record');
      }

      notifications.show({
        title: 'Success',
        message: 'Record deleted successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      loadRecords();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to delete record',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    }
  };

  const handleExport = async (format: 'csv' | 'xlsx' | 'json') => {
    try {
      const params = new URLSearchParams({
        format,
      });

      if (debouncedSearch) {
        params.append('search', debouncedSearch);
      }

      if (statusFilter) {
        params.append('status', statusFilter);
      }

      const response = await fetch(`/api/records/export?${params}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to export records');
      }

      // Handle file download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `records.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      notifications.show({
        title: 'Success',
        message: `Records exported as ${format.toUpperCase()}`,
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to export records',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    }
  };

  const formatFieldValue = (value: any): string => {
    if (value === null || value === undefined) return '-';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (Array.isArray(value)) return value.join(', ');
    if (typeof value === 'object') return JSON.stringify(value);
    return String(value);
  };

  const getDisplayFields = (record: Record) => {
    const allData = { ...record.core_field_data, ...record.custom_field_data };
    // Show first few fields for table display
    const keys = Object.keys(allData).slice(0, 3);
    return keys.map(key => ({
      key,
      value: formatFieldValue(allData[key])
    }));
  };

  if (loading && records.length === 0) {
    return (
      <div style={{ position: 'relative', minHeight: 400 }}>
        <LoadingOverlay visible />
      </div>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
        {error}
      </Alert>
    );
  }

  return (
    <Stack gap="md">
      {/* Header */}
      <Group justify="space-between">
        <div>
          <Title order={3}>Records</Title>
          <Text c="dimmed">Manage your company records ({totalRecords} total)</Text>
        </div>
        <Group>
          <Menu shadow="md" width={200}>
            <Menu.Target>
              <Button variant="outline" leftSection={<IconDownload size={16} />}>
                Export
              </Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item onClick={() => handleExport('csv')}>
                Export as CSV
              </Menu.Item>
              <Menu.Item onClick={() => handleExport('xlsx')}>
                Export as Excel
              </Menu.Item>
              <Menu.Item onClick={() => handleExport('json')}>
                Export as JSON
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() => setCreateModalOpen(true)}
          >
            Create Record
          </Button>
        </Group>
      </Group>

      {/* Filters */}
      <Paper withBorder p="md">
        <Group>
          <TextInput
            placeholder="Search records..."
            leftSection={<IconSearch size={16} />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            style={{ flex: 1 }}
          />
          <Select
            placeholder="Filter by status"
            data={[
              { value: '', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'archived', label: 'Archived' },
            ]}
            value={statusFilter}
            onChange={(value) => setStatusFilter(value || '')}
            clearable
          />
        </Group>
      </Paper>

      {/* Records Table */}
      <Paper withBorder>
        {records.length === 0 ? (
          <Stack align="center" p="xl">
            <Text c="dimmed">No records found</Text>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={() => setCreateModalOpen(true)}
            >
              Create Your First Record
            </Button>
          </Stack>
        ) : (
          <ScrollArea>
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>ID</Table.Th>
                  <Table.Th>Data Preview</Table.Th>
                  <Table.Th>Status</Table.Th>
                  <Table.Th>Created</Table.Th>
                  <Table.Th>Created By</Table.Th>
                  <Table.Th width={100}>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {records.map((record) => {
                  const displayFields = getDisplayFields(record);
                  return (
                    <Table.Tr key={record.id}>
                      <Table.Td>
                        <Text ff="monospace" size="sm">
                          {record.id.slice(0, 8)}...
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Stack gap="xs">
                          {displayFields.map((field) => (
                            <Text key={field.key} size="sm">
                              <Text span fw={500}>{field.key}:</Text> {field.value}
                            </Text>
                          ))}
                        </Stack>
                      </Table.Td>
                      <Table.Td>
                        <Badge
                          color={record.status === 'active' ? 'green' : 'gray'}
                          variant="light"
                        >
                          {record.status}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">
                          {new Date(record.created_at).toLocaleDateString()}
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">
                          {record.created_by_profile?.full_name || 
                           record.created_by_profile?.email || 
                           'Unknown'}
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Menu shadow="md" width={200}>
                          <Menu.Target>
                            <ActionIcon variant="subtle">
                              <IconDots size={16} />
                            </ActionIcon>
                          </Menu.Target>
                          <Menu.Dropdown>
                            <Menu.Item
                              leftSection={<IconEye size={14} />}
                              onClick={() => {
                                setSelectedRecord(record);
                                setViewModalOpen(true);
                              }}
                            >
                              View Details
                            </Menu.Item>
                            <Menu.Item
                              leftSection={<IconEdit size={14} />}
                            >
                              Edit
                            </Menu.Item>
                            <Menu.Divider />
                            <Menu.Item
                              leftSection={<IconTrash size={14} />}
                              color="red"
                              onClick={() => handleDelete(record)}
                            >
                              Delete
                            </Menu.Item>
                          </Menu.Dropdown>
                        </Menu>
                      </Table.Td>
                    </Table.Tr>
                  );
                })}
              </Table.Tbody>
            </Table>
          </ScrollArea>
        )}
      </Paper>

      {/* Pagination */}
      {totalPages > 1 && (
        <Group justify="center">
          <Pagination
            value={currentPage}
            onChange={setCurrentPage}
            total={totalPages}
          />
        </Group>
      )}

      {/* Create Record Modal */}
      <Modal
        opened={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        title="Create New Record"
        size="xl"
      >
        <RecordCreation />
      </Modal>

      {/* View Record Modal */}
      <Modal
        opened={viewModalOpen}
        onClose={() => setViewModalOpen(false)}
        title="Record Details"
        size="lg"
      >
        {selectedRecord && (
          <Stack gap="md">
            <Group>
              <Badge color={selectedRecord.status === 'active' ? 'green' : 'gray'}>
                {selectedRecord.status}
              </Badge>
              <Text size="sm" c="dimmed">
                Created: {new Date(selectedRecord.created_at).toLocaleString()}
              </Text>
            </Group>
            
            <Paper withBorder p="md">
              <Title order={4} mb="md">Core Fields</Title>
              <Stack gap="xs">
                {Object.entries(selectedRecord.core_field_data).map(([key, value]) => (
                  <Group key={key} justify="space-between">
                    <Text fw={500}>{key}:</Text>
                    <Text>{formatFieldValue(value)}</Text>
                  </Group>
                ))}
              </Stack>
            </Paper>

            <Paper withBorder p="md">
              <Title order={4} mb="md">Custom Fields</Title>
              <Stack gap="xs">
                {Object.entries(selectedRecord.custom_field_data).map(([key, value]) => (
                  <Group key={key} justify="space-between">
                    <Text fw={500}>{key}:</Text>
                    <Text>{formatFieldValue(value)}</Text>
                  </Group>
                ))}
              </Stack>
            </Paper>

            {selectedRecord.tags.length > 0 && (
              <Paper withBorder p="md">
                <Title order={4} mb="md">Tags</Title>
                <Group>
                  {selectedRecord.tags.map((tag) => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </Group>
              </Paper>
            )}
          </Stack>
        )}
      </Modal>
    </Stack>
  );
}
