import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { verifyAdminAuth } from 'src/lib/adminAuth';
import { createAuditLog } from 'src/lib/auditLog';

const SUPPORTED_FIELD_TYPES = ['text', 'number', 'dropdown', 'checkbox', 'date', 'email', 'phone', 'price'];

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can access core fields
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const supabase = getSupabaseClient();
    const { id } = params;

    const { data: coreField, error } = await supabase
      .from('core_fields')
      .select(`
        id,
        created_at,
        updated_at,
        name,
        field_key,
        field_type,
        description,
        is_required,
        is_active,
        default_value,
        validation_rules,
        dropdown_options,
        display_order,
        created_by,
        updated_by
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Core field not found' }, { status: 404 });
      }
      console.error('Error fetching core field:', error);
      return NextResponse.json({ error: 'Failed to fetch core field' }, { status: 500 });
    }

    // Get creator/updater information
    const userIds = [coreField.created_by, coreField.updated_by].filter(Boolean);
    let users = [];
    if (userIds.length > 0) {
      const { data: usersData } = await supabase
        .from('profiles')
        .select('id, full_name, email')
        .in('id', userIds);
      users = usersData || [];
    }

    const enrichedCoreField = {
      ...coreField,
      created_by_user: users.find(user => user.id === coreField.created_by),
      updated_by_user: users.find(user => user.id === coreField.updated_by)
    };

    return NextResponse.json({ data: enrichedCoreField });

  } catch (error) {
    console.error('Core field GET API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can update core fields
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const supabase = getSupabaseClient();
    const { id } = params;
    const body = await request.json();

    // Get existing field for audit log
    const { data: existingField, error: fetchError } = await supabase
      .from('core_fields')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Core field not found' }, { status: 404 });
      }
      console.error('Error fetching existing core field:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch core field' }, { status: 500 });
    }

    const {
      name,
      field_type,
      description,
      is_required,
      is_active,
      default_value,
      validation_rules,
      dropdown_options,
      display_order
    } = body;

    // Validation
    if (name !== undefined && !name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    if (field_type !== undefined && !SUPPORTED_FIELD_TYPES.includes(field_type)) {
      return NextResponse.json({ 
        error: `Unsupported field type. Supported types: ${SUPPORTED_FIELD_TYPES.join(', ')}` 
      }, { status: 400 });
    }

    // For dropdown fields, validate that options are provided
    if (field_type === 'dropdown' && (!dropdown_options || !Array.isArray(dropdown_options) || dropdown_options.length === 0)) {
      return NextResponse.json({ 
        error: 'Dropdown fields must have at least one option' 
      }, { status: 400 });
    }

    const userId = authResult.session?.user?.id;

    // Prepare update data
    const updateData: any = {
      updated_by: userId
    };

    if (name !== undefined) updateData.name = name;
    if (field_type !== undefined) updateData.field_type = field_type;
    if (description !== undefined) updateData.description = description;
    if (is_required !== undefined) updateData.is_required = is_required;
    if (is_active !== undefined) updateData.is_active = is_active;
    if (default_value !== undefined) updateData.default_value = default_value;
    if (validation_rules !== undefined) updateData.validation_rules = validation_rules;
    if (dropdown_options !== undefined) updateData.dropdown_options = dropdown_options;
    if (display_order !== undefined) updateData.display_order = display_order;

    // Update the core field
    const { data: updatedField, error: updateError } = await supabase
      .from('core_fields')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating core field:', updateError);
      return NextResponse.json({ error: 'Failed to update core field' }, { status: 500 });
    }

    // Create audit log
    await createAuditLog({
      user_id: userId,
      user_email: authResult.session?.user?.email || '',
      action: 'UPDATE',
      resource_type: 'core_field',
      resource_id: id,
      old_values: existingField,
      new_values: updatedField,
      ip_address: request.ip,
      user_agent: request.headers.get('user-agent')
    });

    return NextResponse.json({ 
      data: updatedField,
      message: 'Core field updated successfully' 
    });

  } catch (error) {
    console.error('Core field PUT API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can delete core fields
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const supabase = getSupabaseClient();
    const { id } = params;

    // Get existing field for audit log
    const { data: existingField, error: fetchError } = await supabase
      .from('core_fields')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Core field not found' }, { status: 404 });
      }
      console.error('Error fetching existing core field:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch core field' }, { status: 500 });
    }

    // Check if field is being used in records (optional - you might want to prevent deletion)
    const { count: recordsCount } = await supabase
      .from('records')
      .select('*', { count: 'exact', head: true })
      .not('core_field_data', 'is', null)
      .textSearch('core_field_data', existingField.field_key);

    if (recordsCount && recordsCount > 0) {
      return NextResponse.json({ 
        error: `Cannot delete core field '${existingField.name}' as it is being used in ${recordsCount} record(s). Consider deactivating it instead.` 
      }, { status: 409 });
    }

    // Delete the core field
    const { error: deleteError } = await supabase
      .from('core_fields')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting core field:', deleteError);
      return NextResponse.json({ error: 'Failed to delete core field' }, { status: 500 });
    }

    const userId = authResult.session?.user?.id;

    // Create audit log
    await createAuditLog({
      user_id: userId,
      user_email: authResult.session?.user?.email || '',
      action: 'DELETE',
      resource_type: 'core_field',
      resource_id: id,
      old_values: existingField,
      ip_address: request.ip,
      user_agent: request.headers.get('user-agent')
    });

    return NextResponse.json({ 
      message: 'Core field deleted successfully' 
    });

  } catch (error) {
    console.error('Core field DELETE API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
