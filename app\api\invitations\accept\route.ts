import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';
import { createAuditLog } from 'src/lib/auditLog';

/**
 * POST /api/invitations/accept
 * Accept a company invitation
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    const body = await request.json();
    const { token } = body;

    if (!token) {
      return NextResponse.json({ error: 'Invitation token is required' }, { status: 400 });
    }

    // Get the invitation
    const { data: invitation, error: invitationError } = await supabase
      .from('company_invitations')
      .select(`
        id,
        company_id,
        email,
        role,
        is_company_admin,
        status,
        expires_at,
        company:company_id(
          id,
          name,
          subscription_status,
          disabled,
          max_users
        )
      `)
      .eq('token', token)
      .single();

    if (invitationError) {
      if (invitationError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Invalid invitation token' }, { status: 404 });
      }
      console.error('Error fetching invitation:', invitationError);
      return NextResponse.json({ error: 'Failed to fetch invitation' }, { status: 500 });
    }

    // Validate invitation
    if (invitation.status !== 'pending') {
      return NextResponse.json({ error: 'Invitation is no longer valid' }, { status: 400 });
    }

    if (new Date(invitation.expires_at) < new Date()) {
      // Mark as expired
      await supabase
        .from('company_invitations')
        .update({ status: 'expired' })
        .eq('id', invitation.id);
      
      return NextResponse.json({ error: 'Invitation has expired' }, { status: 400 });
    }

    if (invitation.email.toLowerCase() !== userEmail.toLowerCase()) {
      return NextResponse.json({ error: 'Invitation is not for this email address' }, { status: 403 });
    }

    // Check if company is active
    const company = invitation.company as any;
    if (!company || company.disabled || company.subscription_status !== 'active') {
      return NextResponse.json({ error: 'Company is not active' }, { status: 403 });
    }

    // Check if user is already in a company
    if (profile.company_id) {
      return NextResponse.json({ error: 'User is already associated with a company' }, { status: 409 });
    }

    // Check company user limits
    const { count: currentUserCount } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .eq('company_id', invitation.company_id);

    if ((currentUserCount || 0) >= company.max_users) {
      return NextResponse.json({ error: 'Company has reached maximum user limit' }, { status: 403 });
    }

    // Start transaction-like operations
    const now = new Date().toISOString();

    // Update user profile to join the company
    const { data: updatedProfile, error: updateProfileError } = await supabase
      .from('profiles')
      .update({
        company_id: invitation.company_id,
        role: invitation.role,
        is_company_admin: invitation.is_company_admin,
        updated_at: now
      })
      .eq('id', profile.id)
      .select()
      .single();

    if (updateProfileError) {
      console.error('Error updating user profile:', updateProfileError);
      return NextResponse.json({ error: 'Failed to join company' }, { status: 500 });
    }

    // Mark invitation as accepted
    const { data: updatedInvitation, error: updateInvitationError } = await supabase
      .from('company_invitations')
      .update({
        status: 'accepted',
        accepted_at: now,
        accepted_by: profile.id,
        updated_at: now
      })
      .eq('id', invitation.id)
      .select(`
        id,
        created_at,
        updated_at,
        company_id,
        invited_by,
        email,
        role,
        is_company_admin,
        status,
        expires_at,
        accepted_at,
        accepted_by,
        metadata,
        company:company_id(id, name, logo_url, subscription_plan)
      `)
      .single();

    if (updateInvitationError) {
      console.error('Error updating invitation:', updateInvitationError);
      // Try to rollback profile update
      await supabase
        .from('profiles')
        .update({
          company_id: null,
          role: 'user',
          is_company_admin: false,
          updated_at: now
        })
        .eq('id', profile.id);
      
      return NextResponse.json({ error: 'Failed to accept invitation' }, { status: 500 });
    }

    // Create audit logs
    await Promise.all([
      // Log profile update
      createAuditLog({
        user_id: profile.id,
        user_email: userEmail,
        company_id: invitation.company_id,
        action: 'UPDATE',
        resource_type: 'profile',
        resource_id: profile.id,
        old_values: { company_id: null, role: 'user', is_company_admin: false },
        new_values: updatedProfile,
        ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        user_agent: request.headers.get('user-agent'),
        metadata: { action: 'accept_invitation', invitation_id: invitation.id }
      }),
      // Log invitation acceptance
      createAuditLog({
        user_id: profile.id,
        user_email: userEmail,
        company_id: invitation.company_id,
        action: 'UPDATE',
        resource_type: 'company_invitation',
        resource_id: invitation.id,
        old_values: { status: 'pending', accepted_at: null, accepted_by: null },
        new_values: updatedInvitation,
        ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        user_agent: request.headers.get('user-agent'),
        metadata: { action: 'accept_invitation' }
      })
    ]);

    return NextResponse.json({
      data: {
        invitation: updatedInvitation,
        profile: updatedProfile
      },
      message: 'Invitation accepted successfully'
    });

  } catch (error) {
    console.error('Accept invitation API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * GET /api/invitations/accept?token=xxx
 * Get invitation details for acceptance page
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json({ error: 'Invitation token is required' }, { status: 400 });
    }

    const supabase = getSupabaseClient();

    // Get the invitation (public endpoint, no auth required)
    const { data: invitation, error: invitationError } = await supabase
      .from('company_invitations')
      .select(`
        id,
        created_at,
        email,
        role,
        is_company_admin,
        status,
        expires_at,
        metadata,
        company:company_id(
          id,
          name,
          logo_url,
          description,
          subscription_plan
        ),
        invited_by_user:invited_by(
          full_name,
          email
        )
      `)
      .eq('token', token)
      .single();

    if (invitationError) {
      if (invitationError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Invalid invitation token' }, { status: 404 });
      }
      console.error('Error fetching invitation:', invitationError);
      return NextResponse.json({ error: 'Failed to fetch invitation' }, { status: 500 });
    }

    // Check if invitation is expired
    const isExpired = new Date(invitation.expires_at) < new Date();
    if (isExpired && invitation.status === 'pending') {
      // Mark as expired
      await supabase
        .from('company_invitations')
        .update({ status: 'expired' })
        .eq('id', invitation.id);
      
      invitation.status = 'expired';
    }

    // Remove sensitive data
    const publicInvitation = {
      ...invitation,
      id: undefined, // Don't expose internal ID
    };

    return NextResponse.json({
      data: publicInvitation,
      isExpired,
      isValid: invitation.status === 'pending' && !isExpired
    });

  } catch (error) {
    console.error('Get invitation API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
