export interface AnalyticsOverview {
  totalCompanies: number;
  totalProfiles: number;
  totalRecords: number;
  totalCoreFields: number;
  totalCustomFields: number;
  activeCompanies: number;
  companyAdmins: number;
}

export interface GrowthTrend {
  date: string;
  companies: number;
  profiles: number;
  records: number;
}

export interface TopCompany {
  id: string;
  name: string;
  slug: string;
  subscription_plan: string;
  profiles: { count: number }[];
  records: { count: number }[];
}

export interface CoreFieldUsage {
  field: {
    id: string;
    name: string;
    field_key: string;
    field_type: string;
    is_active: boolean;
  };
  usageCount: number;
  usagePercentage: number;
}

export interface RecentActivity {
  id: string;
  created_at: string;
  user_email: string;
  action: string;
  resource_type: string;
  resource_id: string | null;
  company_id: string | null;
}

export interface AnalyticsData {
  overview: AnalyticsOverview;
  subscriptionPlans: Record<string, number>;
  subscriptionStatus: Record<string, number>;
  fieldTypes: {
    core: Record<string, number>;
    custom: Record<string, number>;
  };
  growthTrends: GrowthTrend[];
  topCompanies: TopCompany[];
  coreFieldUsage: Record<string, CoreFieldUsage>;
  recentActivity: RecentActivity[];
}

export interface AnalyticsResponse {
  data: AnalyticsData;
  timeRange: string;
  companyId: string | null;
  generatedAt: string;
}

export interface ReportFilters {
  type: 'records' | 'companies' | 'users' | 'fields';
  companyId?: string;
  startDate?: string;
  endDate?: string;
  format?: 'json' | 'csv';
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
}

export interface ReportData {
  data: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  columns: string[];
  reportType: string;
  generatedAt: string;
}

export type TimeRange = '7d' | '30d' | '90d' | '1y';

export const TIME_RANGE_LABELS: Record<TimeRange, string> = {
  '7d': 'Last 7 days',
  '30d': 'Last 30 days',
  '90d': 'Last 90 days',
  '1y': 'Last year'
};

export const REPORT_TYPE_LABELS = {
  records: 'Records Report',
  companies: 'Companies Report',
  users: 'Users Report',
  fields: 'Fields Report'
};

export const CHART_COLORS = {
  primary: '#228be6',
  secondary: '#40c057',
  tertiary: '#fd7e14',
  quaternary: '#e64980',
  success: '#51cf66',
  warning: '#ffd43b',
  error: '#ff6b6b',
  info: '#74c0fc'
};

// Utility functions
export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

export function formatPercentage(value: number, total: number): string {
  if (total === 0) return '0%';
  return Math.round((value / total) * 100) + '%';
}

export function calculateGrowthRate(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return Math.round(((current - previous) / previous) * 100);
}

export function getGrowthColor(growthRate: number): string {
  if (growthRate > 0) return CHART_COLORS.success;
  if (growthRate < 0) return CHART_COLORS.error;
  return CHART_COLORS.info;
}

export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString();
}

export function formatDateTime(dateString: string): string {
  return new Date(dateString).toLocaleString();
}

export function getSubscriptionPlanColor(plan: string): string {
  const colors: Record<string, string> = {
    free: CHART_COLORS.info,
    basic: CHART_COLORS.primary,
    premium: CHART_COLORS.secondary,
    enterprise: CHART_COLORS.tertiary
  };
  return colors[plan] || CHART_COLORS.info;
}

export function getSubscriptionStatusColor(status: string): string {
  const colors: Record<string, string> = {
    active: CHART_COLORS.success,
    suspended: CHART_COLORS.warning,
    cancelled: CHART_COLORS.error
  };
  return colors[status] || CHART_COLORS.info;
}

export function aggregateGrowthTrends(trends: GrowthTrend[], groupBy: 'day' | 'week' | 'month' = 'day'): GrowthTrend[] {
  if (groupBy === 'day') return trends;
  
  const grouped: Record<string, { companies: number; profiles: number; records: number; count: number }> = {};
  
  trends.forEach(trend => {
    const date = new Date(trend.date);
    let key: string;
    
    if (groupBy === 'week') {
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      key = weekStart.toISOString().split('T')[0];
    } else { // month
      key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
    }
    
    if (!grouped[key]) {
      grouped[key] = { companies: 0, profiles: 0, records: 0, count: 0 };
    }
    
    grouped[key].companies += trend.companies;
    grouped[key].profiles += trend.profiles;
    grouped[key].records += trend.records;
    grouped[key].count += 1;
  });
  
  return Object.entries(grouped)
    .map(([date, data]) => ({
      date,
      companies: Math.round(data.companies / data.count),
      profiles: Math.round(data.profiles / data.count),
      records: Math.round(data.records / data.count)
    }))
    .sort((a, b) => a.date.localeCompare(b.date));
}

export function getTopFieldsByUsage(coreFieldUsage: Record<string, CoreFieldUsage>, limit: number = 10): CoreFieldUsage[] {
  return Object.values(coreFieldUsage)
    .sort((a, b) => b.usagePercentage - a.usagePercentage)
    .slice(0, limit);
}

export function getFieldTypeDistribution(fieldTypes: { core: Record<string, number>; custom: Record<string, number> }) {
  const distribution: Record<string, { core: number; custom: number; total: number }> = {};
  
  // Process core fields
  Object.entries(fieldTypes.core).forEach(([type, count]) => {
    if (!distribution[type]) {
      distribution[type] = { core: 0, custom: 0, total: 0 };
    }
    distribution[type].core = count;
    distribution[type].total += count;
  });
  
  // Process custom fields
  Object.entries(fieldTypes.custom).forEach(([type, count]) => {
    if (!distribution[type]) {
      distribution[type] = { core: 0, custom: 0, total: 0 };
    }
    distribution[type].custom = count;
    distribution[type].total += count;
  });
  
  return distribution;
}

export function generateDateRange(startDate: string, endDate: string): string[] {
  const dates: string[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
    dates.push(date.toISOString().split('T')[0]);
  }
  
  return dates;
}

export function fillMissingDates(trends: GrowthTrend[], startDate: string, endDate: string): GrowthTrend[] {
  const dateRange = generateDateRange(startDate, endDate);
  const trendMap = new Map(trends.map(trend => [trend.date, trend]));
  
  return dateRange.map(date => 
    trendMap.get(date) || { date, companies: 0, profiles: 0, records: 0 }
  );
}
