import { CoreField, CoreFieldFormData, CoreFieldsResponse, CoreFieldFilters } from 'src/types/coreFields';

const API_BASE = '/api/admin/core-fields';

export class CoreFieldsApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = 'CoreFieldsApiError';
  }
}

async function handleResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new CoreFieldsApiError(
      errorData.error || `HTTP ${response.status}`,
      response.status,
      errorData.code
    );
  }
  return response.json();
}

/**
 * Fetch core fields with filtering and pagination
 */
export async function fetchCoreFields(filters: CoreFieldFilters = {}): Promise<CoreFieldsResponse> {
  const params = new URLSearchParams();
  
  if (filters.search) params.append('search', filters.search);
  if (filters.fieldType) params.append('fieldType', filters.fieldType);
  if (filters.isActive !== null && filters.isActive !== undefined) {
    params.append('isActive', filters.isActive.toString());
  }
  if (filters.page) params.append('page', filters.page.toString());
  if (filters.limit) params.append('limit', filters.limit.toString());

  const url = `${API_BASE}${params.toString() ? `?${params.toString()}` : ''}`;
  const response = await fetch(url);
  return handleResponse<CoreFieldsResponse>(response);
}

/**
 * Fetch a single core field by ID
 */
export async function fetchCoreField(id: string): Promise<{ data: CoreField }> {
  const response = await fetch(`${API_BASE}/${id}`);
  return handleResponse<{ data: CoreField }>(response);
}

/**
 * Create a new core field
 */
export async function createCoreField(data: CoreFieldFormData): Promise<{ data: CoreField; message: string }> {
  const response = await fetch(API_BASE, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  return handleResponse<{ data: CoreField; message: string }>(response);
}

/**
 * Update an existing core field
 */
export async function updateCoreField(
  id: string, 
  data: Partial<CoreFieldFormData>
): Promise<{ data: CoreField; message: string }> {
  const response = await fetch(`${API_BASE}/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  return handleResponse<{ data: CoreField; message: string }>(response);
}

/**
 * Delete a core field
 */
export async function deleteCoreField(id: string): Promise<{ message: string }> {
  const response = await fetch(`${API_BASE}/${id}`, {
    method: 'DELETE',
  });
  return handleResponse<{ message: string }>(response);
}

/**
 * Toggle core field active status
 */
export async function toggleCoreFieldStatus(id: string, isActive: boolean): Promise<{ data: CoreField; message: string }> {
  return updateCoreField(id, { is_active: isActive });
}

/**
 * Reorder core fields
 */
export async function reorderCoreFields(fieldOrders: { id: string; display_order: number }[]): Promise<void> {
  // Update each field's display order
  const promises = fieldOrders.map(({ id, display_order }) =>
    updateCoreField(id, { display_order })
  );
  
  await Promise.all(promises);
}

/**
 * Bulk update core fields
 */
export async function bulkUpdateCoreFields(
  updates: { id: string; data: Partial<CoreFieldFormData> }[]
): Promise<{ success: number; errors: { id: string; error: string }[] }> {
  const results = await Promise.allSettled(
    updates.map(({ id, data }) => updateCoreField(id, data))
  );

  const success = results.filter(result => result.status === 'fulfilled').length;
  const errors = results
    .map((result, index) => {
      if (result.status === 'rejected') {
        return {
          id: updates[index].id,
          error: result.reason.message || 'Unknown error'
        };
      }
      return null;
    })
    .filter(Boolean) as { id: string; error: string }[];

  return { success, errors };
}

/**
 * Export core fields data
 */
export async function exportCoreFields(format: 'csv' | 'json' = 'csv'): Promise<Blob> {
  const { data: coreFields } = await fetchCoreFields({ limit: 1000 }); // Get all fields
  
  if (format === 'json') {
    const jsonData = JSON.stringify(coreFields, null, 2);
    return new Blob([jsonData], { type: 'application/json' });
  }

  // CSV format
  const headers = [
    'ID',
    'Name',
    'Field Key',
    'Field Type',
    'Description',
    'Required',
    'Active',
    'Default Value',
    'Display Order',
    'Created At',
    'Updated At'
  ];

  const csvRows = [
    headers.join(','),
    ...coreFields.map(field => [
      field.id,
      `"${field.name}"`,
      field.field_key,
      field.field_type,
      `"${field.description || ''}"`,
      field.is_required ? 'Yes' : 'No',
      field.is_active ? 'Yes' : 'No',
      `"${field.default_value || ''}"`,
      field.display_order,
      field.created_at,
      field.updated_at || ''
    ].join(','))
  ];

  const csvContent = csvRows.join('\n');
  return new Blob([csvContent], { type: 'text/csv' });
}

/**
 * Download exported data
 */
export function downloadBlob(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * Validate core field data before submission
 */
export function validateCoreFieldData(data: CoreFieldFormData): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!data.name?.trim()) {
    errors.push('Field name is required');
  }

  if (!data.field_key?.trim()) {
    errors.push('Field key is required');
  } else if (!/^[a-z0-9_]+$/.test(data.field_key)) {
    errors.push('Field key must contain only lowercase letters, numbers, and underscores');
  }

  if (!data.field_type) {
    errors.push('Field type is required');
  }

  if (data.field_type === 'dropdown' && (!data.dropdown_options || data.dropdown_options.length === 0)) {
    errors.push('Dropdown fields must have at least one option');
  }

  if (data.display_order < 0) {
    errors.push('Display order must be a non-negative number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
