'use client';

import { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  Text,
  Group,
  Button,
  TextInput,
  Select,
  Switch,
  Paper,
  Badge,
  ActionIcon,
  Menu,
  Alert,
  LoadingOverlay,
  Pagination,
  Table,
  Tooltip,
  Modal,
} from '@mantine/core';
import {
  IconPlus,
  IconSearch,
  IconFilter,
  IconDownload,
  IconEdit,
  IconTrash,
  IconEye,
  IconEyeOff,
  IconDots,
  IconAlertCircle,
  IconCheck,
  IconX,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import { CoreField, CoreFieldFilters, FIELD_TYPE_LABELS } from 'src/types/coreFields';
import {
  fetchCoreFields,
  deleteCoreField,
  toggleCoreFieldStatus,
  exportCoreFields,
  downloadBlob,
  CoreFieldsApiError
} from 'src/lib/coreFieldsApi';
import { CoreFieldForm } from './CoreFieldForm';

export function CoreFieldsManagement() {
  const [coreFields, setCoreFields] = useState<CoreField[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<CoreFieldFilters>({
    search: '',
    fieldType: '',
    isActive: null,
    page: 1,
    limit: 20
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });
  const [fieldTypes, setFieldTypes] = useState<string[]>([]);
  const [selectedField, setSelectedField] = useState<CoreField | null>(null);
  const [formOpened, { open: openForm, close: closeForm }] = useDisclosure(false);
  const [deleteConfirmOpened, { open: openDeleteConfirm, close: closeDeleteConfirm }] = useDisclosure(false);
  const [fieldToDelete, setFieldToDelete] = useState<CoreField | null>(null);

  // Load core fields
  const loadCoreFields = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetchCoreFields(filters);
      setCoreFields(response.data);
      setPagination(response.pagination);
      setFieldTypes(response.fieldTypes);
    } catch (err) {
      const errorMessage = err instanceof CoreFieldsApiError ? err.message : 'Failed to load core fields';
      setError(errorMessage);
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconX size={16} />
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCoreFields();
  }, [filters]);

  // Handle search
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }));
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof CoreFieldFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // Handle create new field
  const handleCreateField = () => {
    setSelectedField(null);
    openForm();
  };

  // Handle edit field
  const handleEditField = (field: CoreField) => {
    setSelectedField(field);
    openForm();
  };

  // Handle form success
  const handleFormSuccess = () => {
    closeForm();
    loadCoreFields();
    notifications.show({
      title: 'Success',
      message: selectedField ? 'Core field updated successfully' : 'Core field created successfully',
      color: 'green',
      icon: <IconCheck size={16} />
    });
  };

  // Handle toggle field status
  const handleToggleStatus = async (field: CoreField) => {
    try {
      await toggleCoreFieldStatus(field.id, !field.is_active);
      loadCoreFields();
      notifications.show({
        title: 'Success',
        message: `Core field ${!field.is_active ? 'activated' : 'deactivated'} successfully`,
        color: 'green',
        icon: <IconCheck size={16} />
      });
    } catch (err) {
      const errorMessage = err instanceof CoreFieldsApiError ? err.message : 'Failed to update field status';
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconX size={16} />
      });
    }
  };

  // Handle delete field
  const handleDeleteField = (field: CoreField) => {
    setFieldToDelete(field);
    openDeleteConfirm();
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!fieldToDelete) return;

    try {
      await deleteCoreField(fieldToDelete.id);
      closeDeleteConfirm();
      setFieldToDelete(null);
      loadCoreFields();
      notifications.show({
        title: 'Success',
        message: 'Core field deleted successfully',
        color: 'green',
        icon: <IconCheck size={16} />
      });
    } catch (err) {
      const errorMessage = err instanceof CoreFieldsApiError ? err.message : 'Failed to delete field';
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconX size={16} />
      });
    }
  };

  // Handle export
  const handleExport = async (format: 'csv' | 'json') => {
    try {
      const blob = await exportCoreFields(format);
      const filename = `core-fields-${new Date().toISOString().split('T')[0]}.${format}`;
      downloadBlob(blob, filename);
      notifications.show({
        title: 'Success',
        message: `Core fields exported as ${format.toUpperCase()}`,
        color: 'green',
        icon: <IconCheck size={16} />
      });
    } catch (err) {
      notifications.show({
        title: 'Error',
        message: 'Failed to export core fields',
        color: 'red',
        icon: <IconX size={16} />
      });
    }
  };

  return (
    <Stack gap="md">
      {/* Header */}
      <Group justify="space-between">
        <div>
          <Title order={2}>Core Fields Management</Title>
          <Text c="dimmed" size="sm">
            Manage core fields that apply to all companies in the system
          </Text>
        </div>
        <Group>
          <Menu shadow="md" width={200}>
            <Menu.Target>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                Export
              </Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item onClick={() => handleExport('csv')}>
                Export as CSV
              </Menu.Item>
              <Menu.Item onClick={() => handleExport('json')}>
                Export as JSON
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
          <Button leftSection={<IconPlus size={16} />} onClick={handleCreateField}>
            Add Core Field
          </Button>
        </Group>
      </Group>

      {/* Filters */}
      <Paper p="md" withBorder>
        <Group>
          <TextInput
            placeholder="Search fields..."
            leftSection={<IconSearch size={16} />}
            value={filters.search}
            onChange={(e) => handleSearch(e.target.value)}
            style={{ flex: 1 }}
          />
          <Select
            placeholder="Field Type"
            data={[
              { value: '', label: 'All Types' },
              ...fieldTypes.map(type => ({ value: type, label: FIELD_TYPE_LABELS[type as keyof typeof FIELD_TYPE_LABELS] || type }))
            ]}
            value={filters.fieldType}
            onChange={(value) => handleFilterChange('fieldType', value || '')}
            clearable
          />
          <Select
            placeholder="Status"
            data={[
              { value: '', label: 'All Status' },
              { value: 'true', label: 'Active' },
              { value: 'false', label: 'Inactive' }
            ]}
            value={filters.isActive === null ? '' : filters.isActive.toString()}
            onChange={(value) => handleFilterChange('isActive', value === '' ? null : value === 'true')}
            clearable
          />
        </Group>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
      )}

      {/* Core Fields Table */}
      <Paper withBorder style={{ position: 'relative' }}>
        <LoadingOverlay visible={loading} />
        
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Name</Table.Th>
              <Table.Th>Field Key</Table.Th>
              <Table.Th>Type</Table.Th>
              <Table.Th>Required</Table.Th>
              <Table.Th>Status</Table.Th>
              <Table.Th>Order</Table.Th>
              <Table.Th>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {coreFields.map((field) => (
              <Table.Tr key={field.id}>
                <Table.Td>
                  <div>
                    <Text fw={500}>{field.name}</Text>
                    {field.description && (
                      <Text size="xs" c="dimmed">{field.description}</Text>
                    )}
                  </div>
                </Table.Td>
                <Table.Td>
                  <Text ff="monospace" size="sm">{field.field_key}</Text>
                </Table.Td>
                <Table.Td>
                  <Badge variant="light" color="blue">
                    {FIELD_TYPE_LABELS[field.field_type] || field.field_type}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  {field.is_required ? (
                    <Badge color="red" size="sm">Required</Badge>
                  ) : (
                    <Badge color="gray" size="sm">Optional</Badge>
                  )}
                </Table.Td>
                <Table.Td>
                  <Switch
                    checked={field.is_active}
                    onChange={() => handleToggleStatus(field)}
                    size="sm"
                  />
                </Table.Td>
                <Table.Td>
                  <Text size="sm">{field.display_order}</Text>
                </Table.Td>
                <Table.Td>
                  <Group gap="xs">
                    <Tooltip label="Edit">
                      <ActionIcon
                        variant="subtle"
                        color="blue"
                        onClick={() => handleEditField(field)}
                      >
                        <IconEdit size={16} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="Delete">
                      <ActionIcon
                        variant="subtle"
                        color="red"
                        onClick={() => handleDeleteField(field)}
                      >
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>

        {coreFields.length === 0 && !loading && (
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <Text c="dimmed">No core fields found</Text>
          </div>
        )}
      </Paper>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <Group justify="center">
          <Pagination
            value={pagination.page}
            onChange={handlePageChange}
            total={pagination.totalPages}
          />
        </Group>
      )}

      {/* Core Field Form Modal */}
      <Modal
        opened={formOpened}
        onClose={closeForm}
        title={selectedField ? 'Edit Core Field' : 'Create Core Field'}
        size="lg"
      >
        <CoreFieldForm
          field={selectedField}
          onSuccess={handleFormSuccess}
          onCancel={closeForm}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        opened={deleteConfirmOpened}
        onClose={closeDeleteConfirm}
        title="Delete Core Field"
        size="sm"
      >
        <Stack>
          <Text>
            Are you sure you want to delete the core field "{fieldToDelete?.name}"?
            This action cannot be undone.
          </Text>
          <Group justify="flex-end">
            <Button variant="default" onClick={closeDeleteConfirm}>
              Cancel
            </Button>
            <Button color="red" onClick={confirmDelete}>
              Delete
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
}
