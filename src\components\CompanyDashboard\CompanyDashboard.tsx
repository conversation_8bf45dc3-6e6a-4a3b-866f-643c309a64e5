'use client';

import { useState } from 'react';
import {
  Container,
  Tabs,
  Title,
  Text,
  Group,
  ThemeIcon,
  Stack,
  Paper,
  SimpleGrid,
  Card,
  Badge,
  Button,
  ActionIcon,
  Menu,
} from '@mantine/core';
import {
  IconBuilding,
  IconSettings,
  IconUsers,
  IconDatabase,
  IconChartBar,
  IconForms,
  IconPlus,
  IconDots,
  IconEdit,
  IconTrash,
} from '@tabler/icons-react';
import { useUserCompanyStatus } from 'src/hooks/useUserCompanyStatus';
import { CustomFieldsManagement } from './CustomFieldsManagement';
import { RecordManagement } from './RecordManagement';
import { TeamManagement } from './TeamManagement';
import { CompanyAnalytics } from './CompanyAnalytics';

interface CompanyDashboardProps {
  // Props can be added here if needed
}

export function CompanyDashboard({}: CompanyDashboardProps) {
  const { companyName, isCompanyAdmin } = useUserCompanyStatus();
  const [activeTab, setActiveTab] = useState<string>('overview');

  // If not a company admin, show limited view
  if (!isCompanyAdmin) {
    return (
      <Container size="xl" px={0}>
        <Stack gap="xl">
          {/* Header */}
          <Group justify="space-between">
            <Group>
              <ThemeIcon size={40} radius="md" color="blue">
                <IconBuilding size={24} />
              </ThemeIcon>
              <div>
                <Title order={1}>{companyName || 'Company Dashboard'}</Title>
                <Text c="dimmed">Regular User Dashboard</Text>
              </div>
            </Group>
          </Group>

          {/* Regular User Content */}
          <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="md">
            <Card withBorder p="lg">
              <Group justify="space-between" mb="md">
                <ThemeIcon size={40} radius="md" color="blue">
                  <IconForms size={24} />
                </ThemeIcon>
              </Group>
              <Title order={3} mb="xs">Records</Title>
              <Text c="dimmed" size="sm" mb="md">
                View and manage your records
              </Text>
              <Button variant="light" fullWidth>
                View Records
              </Button>
            </Card>

            <Card withBorder p="lg">
              <Group justify="space-between" mb="md">
                <ThemeIcon size={40} radius="md" color="green">
                  <IconPlus size={24} />
                </ThemeIcon>
              </Group>
              <Title order={3} mb="xs">New Record</Title>
              <Text c="dimmed" size="sm" mb="md">
                Create a new record
              </Text>
              <Button variant="light" fullWidth>
                Create Record
              </Button>
            </Card>

            <Card withBorder p="lg">
              <Group justify="space-between" mb="md">
                <ThemeIcon size={40} radius="md" color="orange">
                  <IconSettings size={24} />
                </ThemeIcon>
              </Group>
              <Title order={3} mb="xs">Profile</Title>
              <Text c="dimmed" size="sm" mb="md">
                Manage your profile settings
              </Text>
              <Button variant="light" fullWidth>
                Edit Profile
              </Button>
            </Card>
          </SimpleGrid>
        </Stack>
      </Container>
    );
  }

  // Company Admin Dashboard
  return (
    <Container size="xl" px={0}>
      <Stack gap="xl">
        {/* Header */}
        <Group justify="space-between">
          <Group>
            <ThemeIcon size={40} radius="md" color="blue">
              <IconBuilding size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>{companyName || 'Company Dashboard'}</Title>
              <Text c="dimmed">Company Administrator</Text>
            </div>
          </Group>
        </Group>

        {/* Tabs */}
        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Tab value="overview" leftSection={<IconChartBar size={16} />}>
              Overview
            </Tabs.Tab>
            <Tabs.Tab value="records" leftSection={<IconDatabase size={16} />}>
              Records
            </Tabs.Tab>
            <Tabs.Tab value="custom-fields" leftSection={<IconSettings size={16} />}>
              Custom Fields
            </Tabs.Tab>
            <Tabs.Tab value="team" leftSection={<IconUsers size={16} />}>
              Team
            </Tabs.Tab>
            <Tabs.Tab value="analytics" leftSection={<IconChartBar size={16} />}>
              Analytics
            </Tabs.Tab>
          </Tabs.List>

          {/* Overview Tab */}
          <Tabs.Panel value="overview" pt="md">
            <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="md">
              <Card withBorder p="lg">
                <Group justify="space-between" mb="md">
                  <ThemeIcon size={40} radius="md" color="blue">
                    <IconDatabase size={24} />
                  </ThemeIcon>
                  <Badge color="blue">Active</Badge>
                </Group>
                <Title order={3} mb="xs">Records</Title>
                <Text size="xl" fw={700} mb="xs">1,234</Text>
                <Text c="dimmed" size="sm">
                  Total records in system
                </Text>
              </Card>

              <Card withBorder p="lg">
                <Group justify="space-between" mb="md">
                  <ThemeIcon size={40} radius="md" color="green">
                    <IconUsers size={24} />
                  </ThemeIcon>
                  <Badge color="green">5/10</Badge>
                </Group>
                <Title order={3} mb="xs">Team Members</Title>
                <Text size="xl" fw={700} mb="xs">5</Text>
                <Text c="dimmed" size="sm">
                  Active team members
                </Text>
              </Card>

              <Card withBorder p="lg">
                <Group justify="space-between" mb="md">
                  <ThemeIcon size={40} radius="md" color="orange">
                    <IconSettings size={24} />
                  </ThemeIcon>
                  <Badge color="orange">12</Badge>
                </Group>
                <Title order={3} mb="xs">Custom Fields</Title>
                <Text size="xl" fw={700} mb="xs">12</Text>
                <Text c="dimmed" size="sm">
                  Company-specific fields
                </Text>
              </Card>

              <Card withBorder p="lg">
                <Group justify="space-between" mb="md">
                  <ThemeIcon size={40} radius="md" color="violet">
                    <IconChartBar size={24} />
                  </ThemeIcon>
                  <Badge color="violet">+15%</Badge>
                </Group>
                <Title order={3} mb="xs">Growth</Title>
                <Text size="xl" fw={700} mb="xs">15%</Text>
                <Text c="dimmed" size="sm">
                  This month vs last
                </Text>
              </Card>
            </SimpleGrid>

            {/* Quick Actions */}
            <Paper withBorder p="lg" mt="xl">
              <Title order={3} mb="md">Quick Actions</Title>
              <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="md">
                <Button
                  variant="light"
                  leftSection={<IconPlus size={16} />}
                  fullWidth
                >
                  Create Record
                </Button>
                <Button
                  variant="light"
                  leftSection={<IconUsers size={16} />}
                  fullWidth
                >
                  Invite Team Member
                </Button>
                <Button
                  variant="light"
                  leftSection={<IconSettings size={16} />}
                  fullWidth
                >
                  Add Custom Field
                </Button>
                <Button
                  variant="light"
                  leftSection={<IconChartBar size={16} />}
                  fullWidth
                >
                  View Analytics
                </Button>
              </SimpleGrid>
            </Paper>
          </Tabs.Panel>

          {/* Records Tab */}
          <Tabs.Panel value="records" pt="md">
            <RecordManagement />
          </Tabs.Panel>

          {/* Custom Fields Tab */}
          <Tabs.Panel value="custom-fields" pt="md">
            <CustomFieldsManagement />
          </Tabs.Panel>

          {/* Team Tab */}
          <Tabs.Panel value="team" pt="md">
            <TeamManagement />
          </Tabs.Panel>

          {/* Analytics Tab */}
          <Tabs.Panel value="analytics" pt="md">
            <CompanyAnalytics />
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Container>
  );
}
