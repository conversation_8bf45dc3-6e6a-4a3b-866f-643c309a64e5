import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';
import { createAuditLog } from 'src/lib/auditLog';
import { CompanyInvitation, InvitationFormData } from 'src/types/invitations';
import { randomBytes } from 'crypto';

/**
 * GET /api/company/invitations
 * Get company invitations (for company admins)
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, is_company_admin, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company' }, { status: 403 });
    }

    if (!profile.is_company_admin) {
      return NextResponse.json({ error: 'Company admin access required' }, { status: 403 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status') || '';
    const role = searchParams.get('role') || '';
    const search = searchParams.get('search') || '';
    const offset = (page - 1) * limit;

    // Build query
    let query = supabase
      .from('company_invitations')
      .select(`
        id,
        created_at,
        updated_at,
        company_id,
        invited_by,
        email,
        role,
        is_company_admin,
        status,
        token,
        expires_at,
        accepted_at,
        accepted_by,
        metadata,
        company:company_id(id, name, logo_url, subscription_plan),
        invited_by_user:invited_by(id, full_name, email),
        accepted_by_user:accepted_by(id, full_name, email)
      `, { count: 'exact' })
      .eq('company_id', profile.company_id)
      .order('created_at', { ascending: false });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (role) {
      query = query.eq('role', role);
    }
    if (search) {
      query = query.ilike('email', `%${search}%`);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: invitations, error: invitationsError, count } = await query;

    if (invitationsError) {
      console.error('Error fetching invitations:', invitationsError);
      return NextResponse.json({ error: 'Failed to fetch invitations' }, { status: 500 });
    }

    return NextResponse.json({
      data: invitations || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Invitations GET API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/company/invitations
 * Create a new company invitation
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, is_company_admin, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company' }, { status: 403 });
    }

    if (!profile.is_company_admin) {
      return NextResponse.json({ error: 'Company admin access required' }, { status: 403 });
    }

    const body: InvitationFormData = await request.json();
    const { email, role, is_company_admin, message } = body;

    // Validation
    if (!email || !email.includes('@')) {
      return NextResponse.json({ error: 'Valid email is required' }, { status: 400 });
    }

    if (!['user', 'admin', 'viewer'].includes(role)) {
      return NextResponse.json({ error: 'Invalid role' }, { status: 400 });
    }

    // Check if user is already in the company
    const { data: existingUser } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', email)
      .eq('company_id', profile.company_id)
      .single();

    if (existingUser) {
      return NextResponse.json({ error: 'User is already a member of this company' }, { status: 409 });
    }

    // Check if there's already a pending invitation
    const { data: existingInvitation } = await supabase
      .from('company_invitations')
      .select('id')
      .eq('company_id', profile.company_id)
      .eq('email', email)
      .eq('status', 'pending')
      .single();

    if (existingInvitation) {
      return NextResponse.json({ error: 'Pending invitation already exists for this email' }, { status: 409 });
    }

    // Check company user limits
    const { data: company } = await supabase
      .from('companies')
      .select('max_users, subscription_status, disabled')
      .eq('id', profile.company_id)
      .single();

    if (!company || company.disabled || company.subscription_status !== 'active') {
      return NextResponse.json({ error: 'Company is not active' }, { status: 403 });
    }

    const { count: currentUserCount } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .eq('company_id', profile.company_id);

    if ((currentUserCount || 0) >= company.max_users) {
      return NextResponse.json({ error: 'Company has reached maximum user limit' }, { status: 403 });
    }

    // Generate unique invitation token
    const token = randomBytes(32).toString('hex');

    // Create the invitation
    const { data: newInvitation, error: createError } = await supabase
      .from('company_invitations')
      .insert({
        company_id: profile.company_id,
        invited_by: profile.id,
        email: email.toLowerCase(),
        role,
        is_company_admin: is_company_admin || false,
        token,
        metadata: { message: message || '' }
      })
      .select(`
        id,
        created_at,
        updated_at,
        company_id,
        invited_by,
        email,
        role,
        is_company_admin,
        status,
        token,
        expires_at,
        accepted_at,
        accepted_by,
        metadata,
        company:company_id(id, name, logo_url, subscription_plan),
        invited_by_user:invited_by(id, full_name, email)
      `)
      .single();

    if (createError) {
      console.error('Error creating invitation:', createError);
      return NextResponse.json({ error: 'Failed to create invitation' }, { status: 500 });
    }

    // Create audit log
    await createAuditLog({
      user_id: profile.id,
      user_email: userEmail,
      company_id: profile.company_id,
      action: 'CREATE',
      resource_type: 'company_invitation',
      resource_id: newInvitation.id,
      new_values: newInvitation,
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      user_agent: request.headers.get('user-agent')
    });

    // TODO: Send invitation email
    // await sendInvitationEmail(newInvitation);

    return NextResponse.json({
      data: newInvitation,
      message: 'Invitation sent successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Invitations POST API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
