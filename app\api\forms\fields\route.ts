import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { auth } from 'auth';

export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;

    // Get user profile to determine company
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    // Get active core fields
    const { data: coreFields, error: coreFieldsError } = await supabase
      .from('core_fields')
      .select(`
        id,
        name,
        field_key,
        field_type,
        description,
        is_required,
        is_active,
        default_value,
        validation_rules,
        dropdown_options,
        display_order
      `)
      .eq('is_active', true)
      .order('display_order', { ascending: true });

    if (coreFieldsError) {
      console.error('Error fetching core fields:', coreFieldsError);
      return NextResponse.json({ error: 'Failed to fetch core fields' }, { status: 500 });
    }

    // Get active custom fields for the user's company (if they have one)
    let customFields: any[] = [];
    if (profile.company_id) {
      const { data: customFieldsData, error: customFieldsError } = await supabase
        .from('custom_fields')
        .select(`
          id,
          name,
          field_key,
          field_type,
          description,
          is_required,
          is_active,
          default_value,
          validation_rules,
          dropdown_options,
          display_order
        `)
        .eq('company_id', profile.company_id)
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (customFieldsError) {
        console.error('Error fetching custom fields:', customFieldsError);
        return NextResponse.json({ error: 'Failed to fetch custom fields' }, { status: 500 });
      }

      customFields = customFieldsData || [];
    }

    return NextResponse.json({
      coreFields: coreFields || [],
      customFields,
      userProfile: {
        id: profile.id,
        company_id: profile.company_id
      }
    });

  } catch (error) {
    console.error('Form fields API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
