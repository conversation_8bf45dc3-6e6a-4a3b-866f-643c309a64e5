import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { verifyAdminAuth } from 'src/lib/adminAuth';
import { createAuditLog } from 'src/lib/auditLog';

export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can clear audit logs
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const supabase = getSupabaseClient();
    const userId = authResult.session?.user?.id;
    const userEmail = authResult.session?.user?.email;

    // Get count of logs before deletion for audit purposes
    const { count: logCount, error: countError } = await supabase
      .from('audit_logs')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Error counting audit logs:', countError);
      return NextResponse.json({ error: 'Failed to count audit logs' }, { status: 500 });
    }

    // Delete all audit logs
    const { error: deleteError } = await supabase
      .from('audit_logs')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records (using a condition that's always true)

    if (deleteError) {
      console.error('Error clearing audit logs:', deleteError);
      return NextResponse.json({ error: 'Failed to clear audit logs' }, { status: 500 });
    }

    // Create an audit log entry for this action (after clearing, so it's the only one left)
    await createAuditLog({
      user_id: userId || null,
      user_email: userEmail || 'unknown',
      company_id: authResult.profile?.company_id || null,
      action: 'DELETE',
      resource_type: 'audit_logs',
      resource_id: null,
      old_values: null,
      new_values: null,
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      user_agent: request.headers.get('user-agent'),
      metadata: {
        cleared_count: logCount || 0,
        action_type: 'bulk_clear',
        admin_action: true
      }
    });

    return NextResponse.json({
      success: true,
      message: `Successfully cleared ${logCount || 0} audit log entries`,
      cleared_count: logCount || 0
    });

  } catch (error) {
    console.error('Clear audit logs API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
