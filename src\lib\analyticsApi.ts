import { AnalyticsResponse, ReportFilters, ReportData, TimeRange } from 'src/types/analytics';

const ANALYTICS_API_BASE = '/api/admin/analytics';
const REPORTS_API_BASE = '/api/admin/reports';

export class AnalyticsApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = 'AnalyticsApiError';
  }
}

async function handleResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new AnalyticsApiError(
      errorData.error || `HTTP ${response.status}`,
      response.status,
      errorData.code
    );
  }
  return response.json();
}

/**
 * Fetch analytics data
 */
export async function fetchAnalytics(
  timeRange: TimeRange = '30d',
  companyId?: string
): Promise<AnalyticsResponse> {
  const params = new URLSearchParams();
  params.append('timeRange', timeRange);
  if (companyId) params.append('companyId', companyId);

  const url = `${ANALYTICS_API_BASE}?${params.toString()}`;
  const response = await fetch(url);
  return handleResponse<AnalyticsResponse>(response);
}

/**
 * Fetch report data
 */
export async function fetchReport(filters: ReportFilters): Promise<ReportData> {
  const params = new URLSearchParams();
  
  params.append('type', filters.type);
  if (filters.companyId) params.append('companyId', filters.companyId);
  if (filters.startDate) params.append('startDate', filters.startDate);
  if (filters.endDate) params.append('endDate', filters.endDate);
  if (filters.format) params.append('format', filters.format);
  if (filters.page) params.append('page', filters.page.toString());
  if (filters.limit) params.append('limit', filters.limit.toString());
  if (filters.sortBy) params.append('sortBy', filters.sortBy);
  if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);
  if (filters.filters) params.append('filters', JSON.stringify(filters.filters));

  const url = `${REPORTS_API_BASE}?${params.toString()}`;
  const response = await fetch(url);
  return handleResponse<ReportData>(response);
}

/**
 * Download report as CSV
 */
export async function downloadReport(filters: ReportFilters): Promise<Blob> {
  const csvFilters = { ...filters, format: 'csv' as const };
  const params = new URLSearchParams();
  
  params.append('type', csvFilters.type);
  if (csvFilters.companyId) params.append('companyId', csvFilters.companyId);
  if (csvFilters.startDate) params.append('startDate', csvFilters.startDate);
  if (csvFilters.endDate) params.append('endDate', csvFilters.endDate);
  params.append('format', 'csv');
  if (csvFilters.sortBy) params.append('sortBy', csvFilters.sortBy);
  if (csvFilters.sortOrder) params.append('sortOrder', csvFilters.sortOrder);
  if (csvFilters.filters) params.append('filters', JSON.stringify(csvFilters.filters));

  const url = `${REPORTS_API_BASE}?${params.toString()}`;
  const response = await fetch(url);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new AnalyticsApiError(
      errorData.error || `HTTP ${response.status}`,
      response.status
    );
  }
  
  return response.blob();
}

/**
 * Download exported data
 */
export function downloadBlob(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * Get analytics summary for dashboard
 */
export async function getAnalyticsSummary(timeRange: TimeRange = '30d'): Promise<{
  totalCompanies: number;
  totalUsers: number;
  totalRecords: number;
  activeCompanies: number;
  growthRate: {
    companies: number;
    users: number;
    records: number;
  };
}> {
  const [currentData, previousData] = await Promise.all([
    fetchAnalytics(timeRange),
    fetchAnalytics(timeRange) // In a real implementation, this would fetch previous period data
  ]);

  const current = currentData.data.overview;
  const previous = previousData.data.overview; // This would be actual previous period data

  return {
    totalCompanies: current.totalCompanies,
    totalUsers: current.totalProfiles,
    totalRecords: current.totalRecords,
    activeCompanies: current.activeCompanies,
    growthRate: {
      companies: calculateGrowthRate(current.totalCompanies, previous.totalCompanies),
      users: calculateGrowthRate(current.totalProfiles, previous.totalProfiles),
      records: calculateGrowthRate(current.totalRecords, previous.totalRecords)
    }
  };
}

/**
 * Get company-specific analytics
 */
export async function getCompanyAnalytics(
  companyId: string,
  timeRange: TimeRange = '30d'
): Promise<{
  users: number;
  records: number;
  customFields: number;
  recentActivity: any[];
}> {
  const data = await fetchAnalytics(timeRange, companyId);
  
  return {
    users: data.data.overview.totalProfiles,
    records: data.data.overview.totalRecords,
    customFields: data.data.overview.totalCustomFields,
    recentActivity: data.data.recentActivity
  };
}

/**
 * Get field usage analytics
 */
export async function getFieldUsageAnalytics(timeRange: TimeRange = '30d'): Promise<{
  mostUsedFields: Array<{
    name: string;
    usagePercentage: number;
    usageCount: number;
  }>;
  leastUsedFields: Array<{
    name: string;
    usagePercentage: number;
    usageCount: number;
  }>;
  fieldTypeDistribution: Record<string, number>;
}> {
  const data = await fetchAnalytics(timeRange);
  
  const fieldUsageArray = Object.values(data.data.coreFieldUsage);
  const sortedByUsage = fieldUsageArray.sort((a, b) => b.usagePercentage - a.usagePercentage);
  
  return {
    mostUsedFields: sortedByUsage.slice(0, 5).map(field => ({
      name: field.field.name,
      usagePercentage: field.usagePercentage,
      usageCount: field.usageCount
    })),
    leastUsedFields: sortedByUsage.slice(-5).reverse().map(field => ({
      name: field.field.name,
      usagePercentage: field.usagePercentage,
      usageCount: field.usageCount
    })),
    fieldTypeDistribution: data.data.fieldTypes.core
  };
}

/**
 * Generate custom report with advanced filters
 */
export async function generateCustomReport(config: {
  type: ReportFilters['type'];
  dateRange: { start: string; end: string };
  companyId?: string;
  customFilters?: Record<string, any>;
  columns?: string[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}): Promise<ReportData> {
  const filters: ReportFilters = {
    type: config.type,
    startDate: config.dateRange.start,
    endDate: config.dateRange.end,
    companyId: config.companyId,
    filters: config.customFilters,
    sortBy: config.sortBy || 'created_at',
    sortOrder: config.sortOrder || 'desc',
    limit: 1000 // Large limit for custom reports
  };

  return fetchReport(filters);
}

/**
 * Get real-time metrics
 */
export async function getRealTimeMetrics(): Promise<{
  activeUsers: number;
  recordsCreatedToday: number;
  companiesCreatedToday: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
}> {
  // This would typically connect to a real-time analytics service
  // For now, we'll use the regular analytics API with a short time range
  const data = await fetchAnalytics('7d');
  
  const today = new Date().toISOString().split('T')[0];
  const todayTrends = data.data.growthTrends.find(trend => trend.date === today);
  
  return {
    activeUsers: data.data.overview.totalProfiles,
    recordsCreatedToday: todayTrends?.records || 0,
    companiesCreatedToday: todayTrends?.companies || 0,
    systemHealth: determineSystemHealth(data.data.overview)
  };
}

/**
 * Export analytics data for external analysis
 */
export async function exportAnalyticsData(
  timeRange: TimeRange = '30d',
  format: 'json' | 'csv' = 'json'
): Promise<Blob> {
  const data = await fetchAnalytics(timeRange);
  
  if (format === 'json') {
    const jsonData = JSON.stringify(data, null, 2);
    return new Blob([jsonData], { type: 'application/json' });
  }
  
  // Convert to CSV format
  const csvData = convertAnalyticsToCSV(data.data);
  return new Blob([csvData], { type: 'text/csv' });
}

// Helper functions
function calculateGrowthRate(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return Math.round(((current - previous) / previous) * 100);
}

function determineSystemHealth(overview: any): 'healthy' | 'warning' | 'critical' {
  const activeRatio = overview.activeCompanies / overview.totalCompanies;
  
  if (activeRatio >= 0.9) return 'healthy';
  if (activeRatio >= 0.7) return 'warning';
  return 'critical';
}

function convertAnalyticsToCSV(data: any): string {
  // This is a simplified CSV conversion
  // In a real implementation, you'd want more sophisticated CSV generation
  const rows = [
    ['Metric', 'Value'],
    ['Total Companies', data.overview.totalCompanies],
    ['Total Profiles', data.overview.totalProfiles],
    ['Total Records', data.overview.totalRecords],
    ['Active Companies', data.overview.activeCompanies],
    ['Company Admins', data.overview.companyAdmins]
  ];
  
  return rows.map(row => row.join(',')).join('\n');
}
