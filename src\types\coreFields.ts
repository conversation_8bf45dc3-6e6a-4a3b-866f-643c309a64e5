export type FieldType = 'text' | 'number' | 'dropdown' | 'checkbox' | 'date' | 'email' | 'phone' | 'price';

export interface CoreField {
  id: string;
  created_at: string;
  updated_at: string | null;
  name: string;
  field_key: string;
  field_type: FieldType;
  description: string | null;
  is_required: boolean;
  is_active: boolean;
  default_value: string | null;
  validation_rules: Record<string, any>;
  dropdown_options: string[] | null;
  display_order: number;
  created_by: string | null;
  updated_by: string | null;
  created_by_user?: {
    id: string;
    full_name: string | null;
    email: string | null;
  };
  updated_by_user?: {
    id: string;
    full_name: string | null;
    email: string | null;
  };
}

export interface CoreFieldFormData {
  name: string;
  field_key: string;
  field_type: FieldType;
  description?: string;
  is_required: boolean;
  is_active: boolean;
  default_value?: string;
  validation_rules?: Record<string, any>;
  dropdown_options?: string[];
  display_order: number;
}

export interface CoreFieldsResponse {
  data: CoreField[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  fieldTypes: FieldType[];
}

export interface CoreFieldFilters {
  search?: string;
  fieldType?: FieldType | '';
  isActive?: boolean | null;
  page?: number;
  limit?: number;
}

export const FIELD_TYPE_LABELS: Record<FieldType, string> = {
  text: 'Text',
  number: 'Number',
  dropdown: 'Dropdown',
  checkbox: 'Checkbox',
  date: 'Date',
  email: 'Email',
  phone: 'Phone',
  price: 'Price'
};

export const FIELD_TYPE_DESCRIPTIONS: Record<FieldType, string> = {
  text: 'Single-line text input',
  number: 'Numeric input with validation',
  dropdown: 'Select from predefined options',
  checkbox: 'Boolean true/false checkbox',
  date: 'Date picker input',
  email: 'Email input with validation',
  phone: 'Phone number input with formatting',
  price: 'Currency/price input with formatting'
};

export const DEFAULT_VALIDATION_RULES: Record<FieldType, Record<string, any>> = {
  text: {
    minLength: 0,
    maxLength: 255
  },
  number: {
    min: null,
    max: null,
    decimal: true
  },
  dropdown: {
    allowMultiple: false
  },
  checkbox: {},
  date: {
    minDate: null,
    maxDate: null
  },
  email: {
    allowMultiple: false
  },
  phone: {
    format: 'international'
  },
  price: {
    currency: 'USD',
    min: 0,
    decimal: true
  }
};

// Utility functions
export function generateFieldKey(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .replace(/^_|_$/g, ''); // Remove leading/trailing underscores
}

export function validateFieldKey(fieldKey: string): boolean {
  return /^[a-z0-9_]+$/.test(fieldKey) && fieldKey.length > 0;
}

export function getFieldTypeIcon(fieldType: FieldType): string {
  const icons: Record<FieldType, string> = {
    text: 'IconTextSize',
    number: 'IconNumbers',
    dropdown: 'IconChevronDown',
    checkbox: 'IconSquareCheck',
    date: 'IconCalendar',
    email: 'IconMail',
    phone: 'IconPhone',
    price: 'IconCurrencyDollar'
  };
  return icons[fieldType];
}

export function formatFieldValue(value: any, fieldType: FieldType): string {
  if (value === null || value === undefined) {
    return '';
  }

  switch (fieldType) {
    case 'checkbox':
      return value ? 'Yes' : 'No';
    case 'date':
      return new Date(value).toLocaleDateString();
    case 'price':
      return typeof value === 'number' ? `$${value.toFixed(2)}` : value.toString();
    case 'phone':
      // Basic phone formatting - you might want to use a library like libphonenumber
      return value.toString().replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    default:
      return value.toString();
  }
}

export function validateFieldValue(value: any, field: CoreField): { isValid: boolean; error?: string } {
  // Required field validation
  if (field.is_required && (value === null || value === undefined || value === '')) {
    return { isValid: false, error: `${field.name} is required` };
  }

  // Skip validation if value is empty and field is not required
  if (!field.is_required && (value === null || value === undefined || value === '')) {
    return { isValid: true };
  }

  const rules = field.validation_rules || {};

  switch (field.field_type) {
    case 'text':
      if (rules.minLength && value.length < rules.minLength) {
        return { isValid: false, error: `${field.name} must be at least ${rules.minLength} characters` };
      }
      if (rules.maxLength && value.length > rules.maxLength) {
        return { isValid: false, error: `${field.name} must be no more than ${rules.maxLength} characters` };
      }
      break;

    case 'number':
    case 'price':
      const numValue = parseFloat(value);
      if (isNaN(numValue)) {
        return { isValid: false, error: `${field.name} must be a valid number` };
      }
      if (rules.min !== null && numValue < rules.min) {
        return { isValid: false, error: `${field.name} must be at least ${rules.min}` };
      }
      if (rules.max !== null && numValue > rules.max) {
        return { isValid: false, error: `${field.name} must be no more than ${rules.max}` };
      }
      break;

    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        return { isValid: false, error: `${field.name} must be a valid email address` };
      }
      break;

    case 'dropdown':
      if (field.dropdown_options && !field.dropdown_options.includes(value)) {
        return { isValid: false, error: `${field.name} must be one of the available options` };
      }
      break;

    case 'date':
      const dateValue = new Date(value);
      if (isNaN(dateValue.getTime())) {
        return { isValid: false, error: `${field.name} must be a valid date` };
      }
      if (rules.minDate && dateValue < new Date(rules.minDate)) {
        return { isValid: false, error: `${field.name} must be after ${new Date(rules.minDate).toLocaleDateString()}` };
      }
      if (rules.maxDate && dateValue > new Date(rules.maxDate)) {
        return { isValid: false, error: `${field.name} must be before ${new Date(rules.maxDate).toLocaleDateString()}` };
      }
      break;
  }

  return { isValid: true };
}
