'use client';

import { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  Text,
  Group,
  Button,
  Table,
  Badge,
  ActionIcon,
  Menu,
  Paper,
  Alert,
  LoadingOverlay,
  Switch,
  Modal,
  TextInput,
  Select,
  Textarea,
  NumberInput,
  Checkbox,
  JsonInput,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconPlus,
  IconDots,
  IconEdit,
  IconTrash,
  IconEye,
  IconEyeOff,
  IconAlertCircle,
  IconCheck,
} from '@tabler/icons-react';

interface CustomField {
  id: string;
  name: string;
  field_key: string;
  field_type: string;
  description: string | null;
  is_required: boolean;
  is_active: boolean;
  default_value: string | null;
  validation_rules: Record<string, any>;
  dropdown_options: string[] | null;
  display_order: number;
  created_at: string;
  updated_at: string | null;
}

interface CustomFieldFormData {
  name: string;
  field_key: string;
  field_type: string;
  description: string;
  is_required: boolean;
  is_active: boolean;
  default_value: string;
  validation_rules: string;
  dropdown_options: string;
  display_order: number;
}

const FIELD_TYPES = [
  { value: 'text', label: 'Text' },
  { value: 'number', label: 'Number' },
  { value: 'email', label: 'Email' },
  { value: 'phone', label: 'Phone' },
  { value: 'price', label: 'Price' },
  { value: 'date', label: 'Date' },
  { value: 'dropdown', label: 'Dropdown' },
  { value: 'checkbox', label: 'Checkbox' },
];

export function CustomFieldsManagement() {
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [editingField, setEditingField] = useState<CustomField | null>(null);
  const [submitting, setSubmitting] = useState(false);

  const form = useForm<CustomFieldFormData>({
    initialValues: {
      name: '',
      field_key: '',
      field_type: 'text',
      description: '',
      is_required: false,
      is_active: true,
      default_value: '',
      validation_rules: '{}',
      dropdown_options: '',
      display_order: 0,
    },
    validate: {
      name: (value) => (!value ? 'Name is required' : null),
      field_key: (value) => {
        if (!value) return 'Field key is required';
        if (!/^[a-z0-9_]+$/.test(value)) {
          return 'Field key must contain only lowercase letters, numbers, and underscores';
        }
        return null;
      },
      field_type: (value) => (!value ? 'Field type is required' : null),
    },
  });

  const loadCustomFields = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/company/custom-fields');
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load custom fields');
      }

      const data = await response.json();
      setCustomFields(data.data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load custom fields';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCustomFields();
  }, []);

  const handleOpenModal = (field?: CustomField) => {
    if (field) {
      setEditingField(field);
      form.setValues({
        name: field.name,
        field_key: field.field_key,
        field_type: field.field_type,
        description: field.description || '',
        is_required: field.is_required,
        is_active: field.is_active,
        default_value: field.default_value || '',
        validation_rules: JSON.stringify(field.validation_rules || {}, null, 2),
        dropdown_options: field.dropdown_options?.join('\n') || '',
        display_order: field.display_order,
      });
    } else {
      setEditingField(null);
      form.reset();
      // Set next display order
      const maxOrder = Math.max(...customFields.map(f => f.display_order), -1);
      form.setFieldValue('display_order', maxOrder + 1);
    }
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setEditingField(null);
    form.reset();
  };

  const handleSubmit = async (values: CustomFieldFormData) => {
    try {
      setSubmitting(true);

      // Parse validation rules
      let validationRules = {};
      try {
        validationRules = JSON.parse(values.validation_rules || '{}');
      } catch (e) {
        form.setFieldError('validation_rules', 'Invalid JSON format');
        return;
      }

      // Parse dropdown options
      const dropdownOptions = values.field_type === 'dropdown' 
        ? values.dropdown_options.split('\n').map(opt => opt.trim()).filter(opt => opt)
        : null;

      const payload = {
        name: values.name,
        field_key: values.field_key,
        field_type: values.field_type,
        description: values.description || null,
        is_required: values.is_required,
        is_active: values.is_active,
        default_value: values.default_value || null,
        validation_rules: validationRules,
        dropdown_options: dropdownOptions,
        display_order: values.display_order,
      };

      const url = editingField 
        ? `/api/company/custom-fields/${editingField.id}`
        : '/api/company/custom-fields';
      
      const method = editingField ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${editingField ? 'update' : 'create'} custom field`);
      }

      notifications.show({
        title: 'Success',
        message: `Custom field ${editingField ? 'updated' : 'created'} successfully`,
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      handleCloseModal();
      loadCustomFields();

    } catch (error) {
      console.error('Error submitting custom field:', error);
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to save custom field',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleToggleActive = async (field: CustomField) => {
    try {
      const response = await fetch(`/api/company/custom-fields/${field.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_active: !field.is_active,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update field');
      }

      loadCustomFields();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to update field',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    }
  };

  const handleDelete = async (field: CustomField) => {
    if (!confirm(`Are you sure you want to delete the field "${field.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/company/custom-fields/${field.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete field');
      }

      notifications.show({
        title: 'Success',
        message: 'Custom field deleted successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      loadCustomFields();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to delete field',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    }
  };

  if (loading) {
    return (
      <div style={{ position: 'relative', minHeight: 400 }}>
        <LoadingOverlay visible />
      </div>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
        {error}
      </Alert>
    );
  }

  return (
    <Stack gap="md">
      {/* Header */}
      <Group justify="space-between">
        <div>
          <Title order={3}>Custom Fields</Title>
          <Text c="dimmed">Manage company-specific fields for your records</Text>
        </div>
        <Button
          leftSection={<IconPlus size={16} />}
          onClick={() => handleOpenModal()}
        >
          Add Custom Field
        </Button>
      </Group>

      {/* Fields Table */}
      <Paper withBorder>
        {customFields.length === 0 ? (
          <Stack align="center" p="xl">
            <Text c="dimmed">No custom fields created yet</Text>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={() => handleOpenModal()}
            >
              Create Your First Custom Field
            </Button>
          </Stack>
        ) : (
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Name</Table.Th>
                <Table.Th>Key</Table.Th>
                <Table.Th>Type</Table.Th>
                <Table.Th>Required</Table.Th>
                <Table.Th>Status</Table.Th>
                <Table.Th>Order</Table.Th>
                <Table.Th width={100}>Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {customFields
                .sort((a, b) => a.display_order - b.display_order)
                .map((field) => (
                  <Table.Tr key={field.id}>
                    <Table.Td>
                      <div>
                        <Text fw={500}>{field.name}</Text>
                        {field.description && (
                          <Text size="sm" c="dimmed">{field.description}</Text>
                        )}
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Text ff="monospace" size="sm">{field.field_key}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge variant="light">{field.field_type}</Badge>
                    </Table.Td>
                    <Table.Td>
                      {field.is_required ? (
                        <Badge color="red" size="sm">Required</Badge>
                      ) : (
                        <Badge color="gray" size="sm">Optional</Badge>
                      )}
                    </Table.Td>
                    <Table.Td>
                      <Switch
                        checked={field.is_active}
                        onChange={() => handleToggleActive(field)}
                        size="sm"
                      />
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{field.display_order}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Menu shadow="md" width={200}>
                        <Menu.Target>
                          <ActionIcon variant="subtle">
                            <IconDots size={16} />
                          </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <Menu.Item
                            leftSection={<IconEdit size={14} />}
                            onClick={() => handleOpenModal(field)}
                          >
                            Edit
                          </Menu.Item>
                          <Menu.Item
                            leftSection={field.is_active ? <IconEyeOff size={14} /> : <IconEye size={14} />}
                            onClick={() => handleToggleActive(field)}
                          >
                            {field.is_active ? 'Deactivate' : 'Activate'}
                          </Menu.Item>
                          <Menu.Divider />
                          <Menu.Item
                            leftSection={<IconTrash size={14} />}
                            color="red"
                            onClick={() => handleDelete(field)}
                          >
                            Delete
                          </Menu.Item>
                        </Menu.Dropdown>
                      </Menu>
                    </Table.Td>
                  </Table.Tr>
                ))}
            </Table.Tbody>
          </Table>
        )}
      </Paper>

      {/* Create/Edit Modal */}
      <Modal
        opened={modalOpen}
        onClose={handleCloseModal}
        title={editingField ? 'Edit Custom Field' : 'Create Custom Field'}
        size="lg"
        closeOnClickOutside={!submitting}
        closeOnEscape={!submitting}
      >
        <div style={{ position: 'relative' }}>
          <LoadingOverlay visible={submitting} />
          
          <form onSubmit={form.onSubmit(handleSubmit)}>
            <Stack gap="md">
              <TextInput
                label="Field Name"
                placeholder="Enter field name"
                required
                {...form.getInputProps('name')}
              />

              <TextInput
                label="Field Key"
                placeholder="field_key"
                description="Used for API access. Only lowercase letters, numbers, and underscores."
                required
                {...form.getInputProps('field_key')}
              />

              <Select
                label="Field Type"
                data={FIELD_TYPES}
                required
                {...form.getInputProps('field_type')}
              />

              <Textarea
                label="Description"
                placeholder="Optional description"
                rows={2}
                {...form.getInputProps('description')}
              />

              {form.values.field_type === 'dropdown' && (
                <Textarea
                  label="Dropdown Options"
                  placeholder="Enter each option on a new line"
                  description="Each line will be a separate option"
                  rows={4}
                  {...form.getInputProps('dropdown_options')}
                />
              )}

              <TextInput
                label="Default Value"
                placeholder="Optional default value"
                {...form.getInputProps('default_value')}
              />

              <NumberInput
                label="Display Order"
                description="Lower numbers appear first"
                min={0}
                {...form.getInputProps('display_order')}
              />

              <JsonInput
                label="Validation Rules"
                description="JSON object with validation rules"
                placeholder='{"min": 0, "max": 100}'
                rows={4}
                {...form.getInputProps('validation_rules')}
              />

              <Group>
                <Checkbox
                  label="Required field"
                  {...form.getInputProps('is_required', { type: 'checkbox' })}
                />
                <Checkbox
                  label="Active"
                  {...form.getInputProps('is_active', { type: 'checkbox' })}
                />
              </Group>

              <Group justify="flex-end" mt="md">
                <Button
                  variant="outline"
                  onClick={handleCloseModal}
                  disabled={submitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  loading={submitting}
                >
                  {editingField ? 'Update' : 'Create'} Field
                </Button>
              </Group>
            </Stack>
          </form>
        </div>
      </Modal>
    </Stack>
  );
}
