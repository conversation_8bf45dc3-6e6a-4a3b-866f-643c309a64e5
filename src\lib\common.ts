import { DEFAULT_AVATAR_URL } from './config';

export const getUserValue = (email: string | null | undefined, column: string) => {
  if (typeof localStorage !== "undefined") {
    return localStorage.getItem(`${column}_${email}`);
  }
  return null;
};

export const setUserValue = (email: string | null | undefined, column: string, value: string) => {
  if (typeof localStorage !== "undefined" && column === "colorScheme") {
    if (!localStorage.getItem("colorScheme_" + email)) {
      localStorage.setItem("colorScheme_" + email, "dark");
      value = "dark";
    }
  }
  console.log("set");
  if (typeof localStorage !== "undefined") {
    localStorage.setItem(`${column}_${email}`, value);
    console.log("set", email, column, value);
  }
};

export const deleteUserValue = (email: string | null | undefined, column: string) => {
  if (typeof localStorage !== "undefined") {
    localStorage.removeItem(`${column}_${email}`);
  }
};






/**
 * Detects if the current device is an iOS device (iPhone, iPad, iPod)
 * @returns true if the device is iOS, false otherwise
 */
export const isIOSDevice = (): boolean => {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false;
  }

  const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;

  // Check for iOS devices
  return /iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream;
};

/**
 * Resets iOS Safari zoom by temporarily setting viewport meta tag
 * This fixes the issue where zoom persists after input field interaction
 */
export const resetIOSZoom = (): void => {
  if (!isIOSDevice() || typeof document === 'undefined') {
    return;
  }

  // Find existing viewport meta tag
  let viewportMeta = document.querySelector('meta[name="viewport"]') as HTMLMetaElement;

  if (!viewportMeta) {
    // Create viewport meta tag if it doesn't exist
    viewportMeta = document.createElement('meta');
    viewportMeta.name = 'viewport';
    document.head.appendChild(viewportMeta);
  }

  // Store original content
  const originalContent = viewportMeta.content;

  // Temporarily disable zoom
  viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';

  // Restore original viewport settings after a short delay
  setTimeout(() => {
    if (viewportMeta) {
      viewportMeta.content = originalContent || 'width=device-width, initial-scale=1.0';
    }
  }, 100);
};

