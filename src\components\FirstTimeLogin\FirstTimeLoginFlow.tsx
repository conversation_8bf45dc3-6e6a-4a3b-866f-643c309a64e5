'use client';

import { useState } from 'react';
import {
  Container,
  Paper,
  Title,
  Text,
  Stack,
  Button,
  Group,
  Card,
  Badge,
  Avatar,
  Divider,
  Alert,
  LoadingOverlay,
} from '@mantine/core';
import {
  IconBuilding,
  IconPlus,
  IconMail,
  IconCheck,
  IconAlertCircle,
  IconUsers,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { useUserCompanyStatus } from 'src/hooks/useUserCompanyStatus';
import { CreateCompanyModal } from './CreateCompanyModal';
import { CompanyInvitation } from 'src/types/invitations';

interface FirstTimeLoginFlowProps {
  onComplete: () => void;
}

export function FirstTimeLoginFlow({ onComplete }: FirstTimeLoginFlowProps) {
  const { hasCompany, pendingInvitations, loading, error, refetch } = useUserCompanyStatus();
  const [createCompanyModalOpen, setCreateCompanyModalOpen] = useState(false);
  const [acceptingInvitation, setAcceptingInvitation] = useState<string | null>(null);
  const [decliningInvitation, setDecliningInvitation] = useState<string | null>(null);

  const handleAcceptInvitation = async (invitation: CompanyInvitation) => {
    try {
      setAcceptingInvitation(invitation.id);

      const response = await fetch('/api/invitations/accept', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: invitation.token,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to accept invitation');
      }

      notifications.show({
        title: 'Success',
        message: `Welcome to ${invitation.company?.name}!`,
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      // Refresh status and complete flow
      await refetch();
      onComplete();

    } catch (error) {
      console.error('Error accepting invitation:', error);
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to accept invitation',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setAcceptingInvitation(null);
    }
  };

  const handleDeclineInvitation = async (invitation: CompanyInvitation) => {
    try {
      setDecliningInvitation(invitation.id);

      const response = await fetch('/api/user/invitations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          invitation_id: invitation.id,
          action: 'decline',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to decline invitation');
      }

      notifications.show({
        title: 'Invitation Declined',
        message: 'You have declined the invitation',
        color: 'blue',
      });

      // Refresh invitations
      refetch();

    } catch (error) {
      console.error('Error declining invitation:', error);
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to decline invitation',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setDecliningInvitation(null);
    }
  };

  const handleCompanyCreated = () => {
    setCreateCompanyModalOpen(false);
    refetch();
    onComplete();
  };

  if (loading) {
    return (
      <Container size="md" py="xl">
        <div style={{ position: 'relative', minHeight: 400 }}>
          <LoadingOverlay visible />
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="md" py="xl">
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
      </Container>
    );
  }

  // If user already has a company, complete the flow
  if (hasCompany) {
    onComplete();
    return null;
  }

  return (
    <Container size="md" py="xl">
      <Stack gap="xl">
        {/* Header */}
        <Paper p="xl" withBorder>
          <Stack gap="md" align="center">
            <Avatar size={80} radius="xl">
              <IconBuilding size={40} />
            </Avatar>
            <Title order={1} ta="center">
              Welcome to ODude CRM!
            </Title>
            <Text ta="center" c="dimmed" size="lg">
              Let's get you set up with your company workspace
            </Text>
          </Stack>
        </Paper>

        {/* Pending Invitations */}
        {pendingInvitations.length > 0 && (
          <Paper p="lg" withBorder>
            <Stack gap="md">
              <Group>
                <IconMail size={20} />
                <Title order={3}>Company Invitations</Title>
                <Badge color="blue">{pendingInvitations.length}</Badge>
              </Group>
              <Text c="dimmed">
                You have been invited to join the following companies:
              </Text>

              <Stack gap="md">
                {pendingInvitations.map((invitation: CompanyInvitation) => (
                  <Card key={invitation.id} withBorder>
                    <Group justify="space-between">
                      <Group>
                        <Avatar
                          src={invitation.company?.logo_url}
                          size="md"
                          radius="sm"
                        >
                          <IconBuilding size={20} />
                        </Avatar>
                        <div>
                          <Text fw={500}>{invitation.company?.name}</Text>
                          <Text size="sm" c="dimmed">
                            Invited by {invitation.invited_by_user?.full_name || invitation.invited_by_user?.email}
                          </Text>
                          <Group gap="xs" mt="xs">
                            <Badge size="sm" color="blue">
                              {invitation.role}
                            </Badge>
                            {invitation.is_company_admin && (
                              <Badge size="sm" color="red">
                                Admin
                              </Badge>
                            )}
                          </Group>
                        </div>
                      </Group>
                      <Group gap="xs">
                        <Button
                          size="sm"
                          variant="outline"
                          color="red"
                          onClick={() => handleDeclineInvitation(invitation)}
                          loading={decliningInvitation === invitation.id}
                        >
                          Decline
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleAcceptInvitation(invitation)}
                          loading={acceptingInvitation === invitation.id}
                        >
                          Accept
                        </Button>
                      </Group>
                    </Group>
                  </Card>
                ))}
              </Stack>
            </Stack>
          </Paper>
        )}

        {/* Create New Company */}
        <Paper p="lg" withBorder>
          <Stack gap="md">
            <Group>
              <IconPlus size={20} />
              <Title order={3}>Create New Company</Title>
            </Group>
            <Text c="dimmed">
              Start your own company workspace and become the company administrator.
              You'll be able to invite team members and manage your company's data.
            </Text>
            <Button
              leftSection={<IconBuilding size={16} />}
              onClick={() => setCreateCompanyModalOpen(true)}
              size="md"
            >
              Create Company
            </Button>
          </Stack>
        </Paper>

        {/* Help Text */}
        <Paper p="md" withBorder bg="gray.0">
          <Stack gap="xs">
            <Title order={4}>Need Help?</Title>
            <Text size="sm" c="dimmed">
              • <strong>Accept an invitation</strong> to join an existing company as a team member
            </Text>
            <Text size="sm" c="dimmed">
              • <strong>Create a new company</strong> to start your own workspace and invite others
            </Text>
            <Text size="sm" c="dimmed">
              • You can only be associated with one company at a time
            </Text>
          </Stack>
        </Paper>
      </Stack>

      {/* Create Company Modal */}
      <CreateCompanyModal
        opened={createCompanyModalOpen}
        onClose={() => setCreateCompanyModalOpen(false)}
        onSuccess={handleCompanyCreated}
      />
    </Container>
  );
}
