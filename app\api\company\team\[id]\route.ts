import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';
import { createAuditLog } from 'src/lib/auditLog';

/**
 * PATCH /api/company/team/[id]
 * Update team member (enable/disable, change role, etc.)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;
    const { id } = params;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, is_company_admin, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company' }, { status: 403 });
    }

    if (!profile.is_company_admin) {
      return NextResponse.json({ error: 'Company admin access required' }, { status: 403 });
    }

    // Get existing team member
    const { data: existingMember, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .eq('company_id', profile.company_id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Team member not found' }, { status: 404 });
      }
      console.error('Error fetching existing team member:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch team member' }, { status: 500 });
    }

    // Prevent admin from disabling themselves
    if (existingMember.id === profile.id) {
      return NextResponse.json({ error: 'Cannot modify your own account' }, { status: 400 });
    }

    const body = await request.json();
    const { disabled, role, is_company_admin } = body;

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (typeof disabled === 'boolean') {
      updateData.disabled = disabled;
    }

    if (role !== undefined) {
      if (!['user', 'admin', 'viewer'].includes(role)) {
        return NextResponse.json({ error: 'Invalid role' }, { status: 400 });
      }
      updateData.role = role;
    }

    if (typeof is_company_admin === 'boolean') {
      updateData.is_company_admin = is_company_admin;
    }

    // Update the team member
    const { data: updatedMember, error: updateError } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating team member:', updateError);
      return NextResponse.json({ error: 'Failed to update team member' }, { status: 500 });
    }

    // Create audit log
    await createAuditLog({
      user_id: profile.id,
      user_email: userEmail,
      company_id: profile.company_id,
      action: 'UPDATE',
      resource_type: 'team_member',
      resource_id: id,
      old_values: existingMember,
      new_values: updatedMember,
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      user_agent: request.headers.get('user-agent')
    });

    return NextResponse.json({
      data: updatedMember,
      message: 'Team member updated successfully'
    });

  } catch (error) {
    console.error('Team member PATCH API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
