import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';
import { createAuditLog } from 'src/lib/auditLog';

/**
 * GET /api/company/invitations/[id]
 * Get a specific invitation
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;
    const { id } = params;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, is_company_admin, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company' }, { status: 403 });
    }

    if (!profile.is_company_admin) {
      return NextResponse.json({ error: 'Company admin access required' }, { status: 403 });
    }

    // Get the invitation
    const { data: invitation, error } = await supabase
      .from('company_invitations')
      .select(`
        id,
        created_at,
        updated_at,
        company_id,
        invited_by,
        email,
        role,
        is_company_admin,
        status,
        token,
        expires_at,
        accepted_at,
        accepted_by,
        metadata,
        company:company_id(id, name, logo_url, subscription_plan),
        invited_by_user:invited_by(id, full_name, email),
        accepted_by_user:accepted_by(id, full_name, email)
      `)
      .eq('id', id)
      .eq('company_id', profile.company_id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Invitation not found' }, { status: 404 });
      }
      console.error('Error fetching invitation:', error);
      return NextResponse.json({ error: 'Failed to fetch invitation' }, { status: 500 });
    }

    return NextResponse.json({ data: invitation });

  } catch (error) {
    console.error('Invitation GET API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PATCH /api/company/invitations/[id]
 * Update invitation (resend, cancel, etc.)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;
    const { id } = params;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, is_company_admin, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company' }, { status: 403 });
    }

    if (!profile.is_company_admin) {
      return NextResponse.json({ error: 'Company admin access required' }, { status: 403 });
    }

    // Get existing invitation
    const { data: existingInvitation, error: fetchError } = await supabase
      .from('company_invitations')
      .select('*')
      .eq('id', id)
      .eq('company_id', profile.company_id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Invitation not found' }, { status: 404 });
      }
      console.error('Error fetching existing invitation:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch invitation' }, { status: 500 });
    }

    const body = await request.json();
    const { action, role, is_company_admin } = body;

    let updateData: any = {
      updated_at: new Date().toISOString()
    };

    switch (action) {
      case 'cancel':
        if (existingInvitation.status !== 'pending') {
          return NextResponse.json({ error: 'Can only cancel pending invitations' }, { status: 400 });
        }
        updateData.status = 'declined';
        break;

      case 'resend':
        if (existingInvitation.status !== 'pending') {
          return NextResponse.json({ error: 'Can only resend pending invitations' }, { status: 400 });
        }
        // Extend expiration by 7 days
        updateData.expires_at = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString();
        break;

      case 'update_role':
        if (existingInvitation.status !== 'pending') {
          return NextResponse.json({ error: 'Can only update role for pending invitations' }, { status: 400 });
        }
        if (role && ['user', 'admin', 'viewer'].includes(role)) {
          updateData.role = role;
        }
        if (typeof is_company_admin === 'boolean') {
          updateData.is_company_admin = is_company_admin;
        }
        break;

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    // Update the invitation
    const { data: updatedInvitation, error: updateError } = await supabase
      .from('company_invitations')
      .update(updateData)
      .eq('id', id)
      .select(`
        id,
        created_at,
        updated_at,
        company_id,
        invited_by,
        email,
        role,
        is_company_admin,
        status,
        token,
        expires_at,
        accepted_at,
        accepted_by,
        metadata,
        company:company_id(id, name, logo_url, subscription_plan),
        invited_by_user:invited_by(id, full_name, email),
        accepted_by_user:accepted_by(id, full_name, email)
      `)
      .single();

    if (updateError) {
      console.error('Error updating invitation:', updateError);
      return NextResponse.json({ error: 'Failed to update invitation' }, { status: 500 });
    }

    // Create audit log
    await createAuditLog({
      user_id: profile.id,
      user_email: userEmail,
      company_id: profile.company_id,
      action: 'UPDATE',
      resource_type: 'company_invitation',
      resource_id: id,
      old_values: existingInvitation,
      new_values: updatedInvitation,
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      user_agent: request.headers.get('user-agent'),
      metadata: { action }
    });

    return NextResponse.json({
      data: updatedInvitation,
      message: `Invitation ${action}ed successfully`
    });

  } catch (error) {
    console.error('Invitation PATCH API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE /api/company/invitations/[id]
 * Delete an invitation
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = getSupabaseClient();
    const userEmail = session.user.email;
    const { id } = params;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, company_id, is_company_admin, disabled')
      .eq('email', userEmail)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.disabled) {
      return NextResponse.json({ error: 'User account is disabled' }, { status: 403 });
    }

    if (!profile.company_id) {
      return NextResponse.json({ error: 'User must be associated with a company' }, { status: 403 });
    }

    if (!profile.is_company_admin) {
      return NextResponse.json({ error: 'Company admin access required' }, { status: 403 });
    }

    // Get existing invitation for audit log
    const { data: existingInvitation, error: fetchError } = await supabase
      .from('company_invitations')
      .select('*')
      .eq('id', id)
      .eq('company_id', profile.company_id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Invitation not found' }, { status: 404 });
      }
      console.error('Error fetching existing invitation:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch invitation' }, { status: 500 });
    }

    // Delete the invitation
    const { error: deleteError } = await supabase
      .from('company_invitations')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting invitation:', deleteError);
      return NextResponse.json({ error: 'Failed to delete invitation' }, { status: 500 });
    }

    // Create audit log
    await createAuditLog({
      user_id: profile.id,
      user_email: userEmail,
      company_id: profile.company_id,
      action: 'DELETE',
      resource_type: 'company_invitation',
      resource_id: id,
      old_values: existingInvitation,
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      user_agent: request.headers.get('user-agent')
    });

    return NextResponse.json({ message: 'Invitation deleted successfully' });

  } catch (error) {
    console.error('Invitation DELETE API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
