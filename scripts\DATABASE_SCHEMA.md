# Multi-Tenant CRM Database Schema

This document describes the comprehensive database schema for the ODude CRM Multi-Tenant SaaS Platform.

## Overview

The database is designed to support a multi-tenant SaaS application with the following key features:
- Multi-tenant company management with data isolation
- Admin-defined core fields that apply to all companies
- Company-specific custom fields
- Dynamic record storage with flexible field data
- Comprehensive audit logging
- User management with company associations

## Tables

### 1. Companies (`companies`)
**Purpose**: Manages tenant companies in the multi-tenant system

| Column | Type | Description |
|--------|------|-------------|
| `id` | UUID | Primary key |
| `name` | TEXT | Company name |
| `slug` | TEXT | URL-friendly identifier (unique) |
| `domain` | TEXT | Optional custom domain |
| `logo_url` | TEXT | Company logo URL |
| `description` | TEXT | Company description |
| `subscription_plan` | TEXT | Subscription tier (free, basic, premium, enterprise) |
| `subscription_status` | TEXT | Status (active, suspended, cancelled) |
| `max_users` | INTEGER | Maximum users allowed |
| `max_records` | INTEGER | Maximum records allowed |
| `settings` | JSONB | Company-specific settings |
| `disabled` | BOOLEAN | Whether company is disabled |
| `created_at` | TIMESTAMPTZ | Creation timestamp |
| `updated_at` | TIMESTAMPTZ | Last update timestamp |

### 2. Profiles (`profiles`)
**Purpose**: User profiles with company associations

| Column | Type | Description |
|--------|------|-------------|
| `id` | UUID | Primary key |
| `email` | TEXT | User email (unique) |
| `full_name` | TEXT | User's full name |
| `avatar_url` | TEXT | Profile picture URL |
| `company_id` | UUID | Associated company (FK to companies) |
| `role` | TEXT | User role (admin, user, viewer) |
| `is_company_admin` | BOOLEAN | Whether user is company admin |
| `disabled` | BOOLEAN | Whether account is disabled |
| `last_login_at` | TIMESTAMPTZ | Last login timestamp |
| `timezone` | TEXT | User's timezone |
| `created_at` | TIMESTAMPTZ | Creation timestamp |
| `updated_at` | TIMESTAMPTZ | Last update timestamp |

### 3. Core Fields (`core_fields`)
**Purpose**: Admin-defined fields that apply to all companies

| Column | Type | Description |
|--------|------|-------------|
| `id` | UUID | Primary key |
| `name` | TEXT | Display name of the field |
| `field_key` | TEXT | Unique programmatic identifier |
| `field_type` | TEXT | Field type (text, number, dropdown, checkbox, date, email, phone, price) |
| `description` | TEXT | Field description |
| `is_required` | BOOLEAN | Whether field is mandatory |
| `is_active` | BOOLEAN | Whether field is active |
| `default_value` | TEXT | Default value |
| `validation_rules` | JSONB | Validation rules |
| `dropdown_options` | JSONB | Options for dropdown fields |
| `display_order` | INTEGER | Display order |
| `created_by` | UUID | User who created the field |
| `updated_by` | UUID | User who last updated the field |
| `created_at` | TIMESTAMPTZ | Creation timestamp |
| `updated_at` | TIMESTAMPTZ | Last update timestamp |

### 4. Custom Fields (`custom_fields`)
**Purpose**: Company-specific custom fields

| Column | Type | Description |
|--------|------|-------------|
| `id` | UUID | Primary key |
| `company_id` | UUID | Owning company (FK to companies) |
| `name` | TEXT | Display name of the field |
| `field_key` | TEXT | Field identifier (unique within company) |
| `field_type` | TEXT | Field type |
| `description` | TEXT | Field description |
| `is_required` | BOOLEAN | Whether field is mandatory |
| `is_active` | BOOLEAN | Whether field is active |
| `default_value` | TEXT | Default value |
| `validation_rules` | JSONB | Validation rules |
| `dropdown_options` | JSONB | Options for dropdown fields |
| `display_order` | INTEGER | Display order |
| `created_by` | UUID | User who created the field |
| `updated_by` | UUID | User who last updated the field |
| `created_at` | TIMESTAMPTZ | Creation timestamp |
| `updated_at` | TIMESTAMPTZ | Last update timestamp |

### 5. Records (`records`)
**Purpose**: Dynamic storage for actual data submitted by users

| Column | Type | Description |
|--------|------|-------------|
| `id` | UUID | Primary key |
| `company_id` | UUID | Owning company (FK to companies) |
| `created_by` | UUID | User who created the record |
| `updated_by` | UUID | User who last updated the record |
| `core_field_data` | JSONB | Data for core fields |
| `custom_field_data` | JSONB | Data for custom fields |
| `status` | TEXT | Record status (active, archived, deleted) |
| `tags` | TEXT[] | Array of tags for categorization |
| `metadata` | JSONB | Additional metadata |
| `created_at` | TIMESTAMPTZ | Creation timestamp |
| `updated_at` | TIMESTAMPTZ | Last update timestamp |

### 6. Audit Logs (`audit_logs`)
**Purpose**: Comprehensive audit trail for all system actions

| Column | Type | Description |
|--------|------|-------------|
| `id` | UUID | Primary key |
| `user_id` | UUID | User who performed the action |
| `user_email` | TEXT | User email (preserved even if user deleted) |
| `company_id` | UUID | Associated company |
| `action` | TEXT | Action type (CREATE, UPDATE, DELETE, LOGIN, etc.) |
| `resource_type` | TEXT | Type of resource affected |
| `resource_id` | UUID | ID of the affected resource |
| `old_values` | JSONB | Previous values (for updates/deletes) |
| `new_values` | JSONB | New values (for creates/updates) |
| `ip_address` | INET | IP address of the user |
| `user_agent` | TEXT | User agent string |
| `metadata` | JSONB | Additional metadata |
| `created_at` | TIMESTAMPTZ | Action timestamp |

### 7. Settings (`settings`)
**Purpose**: User-specific settings and configurations

| Column | Type | Description |
|--------|------|-------------|
| `email` | TEXT | User email (primary key) |
| `max_contact_limit` | INTEGER | Maximum contacts allowed |
| `created_at` | TIMESTAMPTZ | Creation timestamp |
| `updated_at` | TIMESTAMPTZ | Last update timestamp |

## Field Types Supported

The system supports the following field types:
- `text`: Single-line text input
- `number`: Numeric input with validation
- `dropdown`: Select from predefined options
- `checkbox`: Boolean true/false
- `date`: Date picker
- `email`: Email input with validation
- `phone`: Phone number input with formatting
- `price`: Currency/price input with formatting

## Sample Core Fields

The system comes pre-configured with these core fields:
- Customer Name (text, required)
- Email Address (email, required)
- Phone Number (phone, optional)
- Order Status (dropdown, required) - Options: Pending, Processing, Shipped, Delivered, Cancelled
- Order Value (price, optional)
- Order Date (date, required)
- Payment Method (dropdown, required) - Options: COD, Advance Paid, Credit Card, Bank Transfer, UPI
- Is Priority (checkbox, optional)

## Multi-Tenancy

Data isolation is achieved through:
1. All company-specific data includes `company_id` foreign key
2. Row-level security policies (to be implemented)
3. Application-level filtering by company context
4. Proper indexing on `company_id` columns

## Indexing Strategy

Key indexes for performance:
- Company-based indexes for multi-tenant queries
- Email-based indexes for user lookups
- Field key indexes for dynamic field access
- Timestamp indexes for audit trails and reporting
- GIN indexes on JSONB columns for flexible querying

## Security Considerations

1. **Data Isolation**: All queries must include company context
2. **Audit Trail**: All changes are logged with user and timestamp
3. **Soft Deletes**: Important data uses status flags rather than hard deletes
4. **Validation**: Field-level validation rules stored in JSONB
5. **Access Control**: Role-based permissions through user roles

## Usage Examples

### Creating a Core Field
```sql
INSERT INTO core_fields (name, field_key, field_type, description, is_required, is_active)
VALUES ('Customer Phone', 'customer_phone', 'phone', 'Customer contact number', false, true);
```

### Creating a Custom Field for a Company
```sql
INSERT INTO custom_fields (company_id, name, field_key, field_type, description)
VALUES ('company-uuid', 'Lead Source', 'lead_source', 'dropdown', 'Where the lead came from');
```

### Storing Record Data
```sql
INSERT INTO records (company_id, created_by, core_field_data, custom_field_data)
VALUES (
  'company-uuid',
  'user-uuid',
  '{"customer_name": "John Doe", "email": "<EMAIL>", "order_status": "Pending"}',
  '{"lead_source": "Website"}'
);
```

## Migration and Setup

Run the `fresh-database-setup.sql` script to create all tables, indexes, triggers, and sample data.

The script includes:
- Table creation with proper constraints
- Index creation for performance
- Trigger functions for automatic timestamps
- Sample core fields for immediate use
- Verification queries to confirm setup
