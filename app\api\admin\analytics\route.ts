import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { verifyAdminAuth } from 'src/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can access analytics
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '30d'; // 7d, 30d, 90d, 1y
    const companyId = searchParams.get('companyId'); // Optional filter by company

    const supabase = getSupabaseClient();

    // Calculate date range
    const now = new Date();
    let startDate: Date;
    
    switch (timeRange) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default: // 30d
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get overall statistics
    const [
      companiesResult,
      profilesResult,
      recordsResult,
      coreFieldsResult,
      customFieldsResult
    ] = await Promise.all([
      // Companies stats
      supabase
        .from('companies')
        .select('id, subscription_plan, subscription_status, created_at, disabled')
        .gte('created_at', startDate.toISOString()),
      
      // Profiles stats
      supabase
        .from('profiles')
        .select('id, company_id, created_at, last_login_at, is_company_admin')
        .gte('created_at', startDate.toISOString()),
      
      // Records stats
      supabase
        .from('records')
        .select('id, company_id, created_at, status, core_field_data')
        .gte('created_at', startDate.toISOString()),
      
      // Core fields stats
      supabase
        .from('core_fields')
        .select('id, field_type, is_active, created_at'),
      
      // Custom fields stats
      supabase
        .from('custom_fields')
        .select('id, company_id, field_type, is_active, created_at')
        .gte('created_at', startDate.toISOString())
    ]);

    // Check for errors
    if (companiesResult.error) throw companiesResult.error;
    if (profilesResult.error) throw profilesResult.error;
    if (recordsResult.error) throw recordsResult.error;
    if (coreFieldsResult.error) throw coreFieldsResult.error;
    if (customFieldsResult.error) throw customFieldsResult.error;

    const companies = companiesResult.data || [];
    const profiles = profilesResult.data || [];
    const records = recordsResult.data || [];
    const coreFields = coreFieldsResult.data || [];
    const customFields = customFieldsResult.data || [];

    // Apply company filter if specified
    const filteredProfiles = companyId ? profiles.filter(p => p.company_id === companyId) : profiles;
    const filteredRecords = companyId ? records.filter(r => r.company_id === companyId) : records;
    const filteredCustomFields = companyId ? customFields.filter(cf => cf.company_id === companyId) : customFields;

    // Calculate metrics
    const metrics = {
      overview: {
        totalCompanies: companies.length,
        totalProfiles: filteredProfiles.length,
        totalRecords: filteredRecords.length,
        totalCoreFields: coreFields.length,
        totalCustomFields: filteredCustomFields.length,
        activeCompanies: companies.filter(c => c.subscription_status === 'active' && !c.disabled).length,
        companyAdmins: filteredProfiles.filter(p => p.is_company_admin).length
      },
      
      // Company distribution by subscription plan
      subscriptionPlans: companies.reduce((acc, company) => {
        acc[company.subscription_plan] = (acc[company.subscription_plan] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      
      // Company distribution by status
      subscriptionStatus: companies.reduce((acc, company) => {
        acc[company.subscription_status] = (acc[company.subscription_status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      
      // Field type distribution
      fieldTypes: {
        core: coreFields.reduce((acc, field) => {
          acc[field.field_type] = (acc[field.field_type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        custom: filteredCustomFields.reduce((acc, field) => {
          acc[field.field_type] = (acc[field.field_type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      },
      
      // Growth trends (daily data for the time range)
      growthTrends: generateGrowthTrends(companies, filteredProfiles, filteredRecords, startDate, now),
      
      // Top companies by activity
      topCompanies: await getTopCompaniesByActivity(supabase, startDate, companyId),
      
      // Core field usage analytics
      coreFieldUsage: await analyzeCoreFieldUsage(supabase, coreFields, filteredRecords),
      
      // Recent activity
      recentActivity: await getRecentActivity(supabase, startDate, companyId)
    };

    return NextResponse.json({
      data: metrics,
      timeRange,
      companyId,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to generate growth trends
function generateGrowthTrends(
  companies: any[],
  profiles: any[],
  records: any[],
  startDate: Date,
  endDate: Date
) {
  const trends = [];
  const dayMs = 24 * 60 * 60 * 1000;
  
  for (let date = new Date(startDate); date <= endDate; date.setTime(date.getTime() + dayMs)) {
    const dayStart = new Date(date);
    const dayEnd = new Date(date.getTime() + dayMs);
    
    const dayCompanies = companies.filter(c => {
      const createdAt = new Date(c.created_at);
      return createdAt >= dayStart && createdAt < dayEnd;
    }).length;
    
    const dayProfiles = profiles.filter(p => {
      const createdAt = new Date(p.created_at);
      return createdAt >= dayStart && createdAt < dayEnd;
    }).length;
    
    const dayRecords = records.filter(r => {
      const createdAt = new Date(r.created_at);
      return createdAt >= dayStart && createdAt < dayEnd;
    }).length;
    
    trends.push({
      date: dayStart.toISOString().split('T')[0],
      companies: dayCompanies,
      profiles: dayProfiles,
      records: dayRecords
    });
  }
  
  return trends;
}

// Helper function to get top companies by activity
async function getTopCompaniesByActivity(supabase: any, startDate: Date, companyId?: string | null) {
  let query = supabase
    .from('companies')
    .select(`
      id,
      name,
      slug,
      subscription_plan,
      profiles:profiles(count),
      records:records(count)
    `)
    .order('created_at', { ascending: false })
    .limit(10);
    
  if (companyId) {
    query = query.eq('id', companyId);
  }
  
  const { data: topCompanies } = await query;
  return topCompanies || [];
}

// Helper function to analyze core field usage
async function analyzeCoreFieldUsage(supabase: any, coreFields: any[], records: any[]) {
  const usage: Record<string, { field: any; usageCount: number; usagePercentage: number }> = {};
  
  coreFields.forEach(field => {
    const usageCount = records.filter(record => {
      const coreData = record.core_field_data || {};
      return coreData[field.field_key] !== undefined && coreData[field.field_key] !== null && coreData[field.field_key] !== '';
    }).length;
    
    usage[field.field_key] = {
      field,
      usageCount,
      usagePercentage: records.length > 0 ? Math.round((usageCount / records.length) * 100) : 0
    };
  });
  
  return usage;
}

// Helper function to get recent activity
async function getRecentActivity(supabase: any, startDate: Date, companyId?: string | null) {
  let auditQuery = supabase
    .from('audit_logs')
    .select('*')
    .gte('created_at', startDate.toISOString())
    .order('created_at', { ascending: false })
    .limit(50);
    
  if (companyId) {
    auditQuery = auditQuery.eq('company_id', companyId);
  }
  
  const { data: recentActivity } = await auditQuery;
  return recentActivity || [];
}
