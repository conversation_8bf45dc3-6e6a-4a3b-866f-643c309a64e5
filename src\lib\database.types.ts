export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      companies: {
        Row: {
          id: string
          created_at: string
          updated_at: string | null
          name: string
          slug: string
          domain: string | null
          logo_url: string | null
          description: string | null
          subscription_plan: string
          subscription_status: string
          max_users: number
          max_records: number
          settings: Json
          disabled: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string | null
          name: string
          slug: string
          domain?: string | null
          logo_url?: string | null
          description?: string | null
          subscription_plan?: string
          subscription_status?: string
          max_users?: number
          max_records?: number
          settings?: <PERSON><PERSON>
          disabled?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string | null
          name?: string
          slug?: string
          domain?: string | null
          logo_url?: string | null
          description?: string | null
          subscription_plan?: string
          subscription_status?: string
          max_users?: number
          max_records?: number
          settings?: Json
          disabled?: boolean
        }
      }
      company_invitations: {
        Row: {
          id: string
          created_at: string
          updated_at: string | null
          company_id: string
          invited_by: string
          email: string
          role: string
          is_company_admin: boolean
          status: string
          token: string
          expires_at: string
          accepted_at: string | null
          accepted_by: string | null
          metadata: Json
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string | null
          company_id: string
          invited_by: string
          email: string
          role?: string
          is_company_admin?: boolean
          status?: string
          token: string
          expires_at?: string
          accepted_at?: string | null
          accepted_by?: string | null
          metadata?: Json
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string | null
          company_id?: string
          invited_by?: string
          email?: string
          role?: string
          is_company_admin?: boolean
          status?: string
          token?: string
          expires_at?: string
          accepted_at?: string | null
          accepted_by?: string | null
          metadata?: Json
        }
      }
      profiles: {
        Row: {
          id: string
          created_at: string
          updated_at: string | null
          full_name: string | null
          avatar_url: string | null
          email: string | null
          disabled: boolean
          company_id: string | null
          role: string
          is_company_admin: boolean
          last_login_at: string | null
          timezone: string
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string | null
          full_name?: string | null
          avatar_url?: string | null
          email?: string | null
          disabled?: boolean
          company_id?: string | null
          role?: string
          is_company_admin?: boolean
          last_login_at?: string | null
          timezone?: string
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string | null
          full_name?: string | null
          avatar_url?: string | null
          email?: string | null
          disabled?: boolean
          company_id?: string | null
          role?: string
          is_company_admin?: boolean
          last_login_at?: string | null
          timezone?: string
        }
      }
      core_fields: {
        Row: {
          id: string
          created_at: string
          updated_at: string | null
          name: string
          field_key: string
          field_type: string
          description: string | null
          is_required: boolean
          is_active: boolean
          default_value: string | null
          validation_rules: Json
          dropdown_options: Json | null
          display_order: number
          created_by: string | null
          updated_by: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string | null
          name: string
          field_key: string
          field_type: string
          description?: string | null
          is_required?: boolean
          is_active?: boolean
          default_value?: string | null
          validation_rules?: Json
          dropdown_options?: Json | null
          display_order?: number
          created_by?: string | null
          updated_by?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string | null
          name?: string
          field_key?: string
          field_type?: string
          description?: string | null
          is_required?: boolean
          is_active?: boolean
          default_value?: string | null
          validation_rules?: Json
          dropdown_options?: Json | null
          display_order?: number
          created_by?: string | null
          updated_by?: string | null
        }
      }
      custom_fields: {
        Row: {
          id: string
          created_at: string
          updated_at: string | null
          company_id: string
          name: string
          field_key: string
          field_type: string
          description: string | null
          is_required: boolean
          is_active: boolean
          default_value: string | null
          validation_rules: Json
          dropdown_options: Json | null
          display_order: number
          created_by: string | null
          updated_by: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string | null
          company_id: string
          name: string
          field_key: string
          field_type: string
          description?: string | null
          is_required?: boolean
          is_active?: boolean
          default_value?: string | null
          validation_rules?: Json
          dropdown_options?: Json | null
          display_order?: number
          created_by?: string | null
          updated_by?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string | null
          company_id?: string
          name?: string
          field_key?: string
          field_type?: string
          description?: string | null
          is_required?: boolean
          is_active?: boolean
          default_value?: string | null
          validation_rules?: Json
          dropdown_options?: Json | null
          display_order?: number
          created_by?: string | null
          updated_by?: string | null
        }
      }
      records: {
        Row: {
          id: string
          created_at: string
          updated_at: string | null
          company_id: string
          created_by: string | null
          updated_by: string | null
          core_field_data: Json
          custom_field_data: Json
          status: string
          tags: string[] | null
          metadata: Json
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string | null
          company_id: string
          created_by?: string | null
          updated_by?: string | null
          core_field_data?: Json
          custom_field_data?: Json
          status?: string
          tags?: string[] | null
          metadata?: Json
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string | null
          company_id?: string
          created_by?: string | null
          updated_by?: string | null
          core_field_data?: Json
          custom_field_data?: Json
          status?: string
          tags?: string[] | null
          metadata?: Json
        }
      }
      audit_logs: {
        Row: {
          id: string
          created_at: string
          user_id: string | null
          user_email: string
          company_id: string | null
          action: string
          resource_type: string
          resource_id: string | null
          old_values: Json | null
          new_values: Json | null
          ip_address: string | null
          user_agent: string | null
          metadata: Json
        }
        Insert: {
          id?: string
          created_at?: string
          user_id?: string | null
          user_email: string
          company_id?: string | null
          action: string
          resource_type: string
          resource_id?: string | null
          old_values?: Json | null
          new_values?: Json | null
          ip_address?: string | null
          user_agent?: string | null
          metadata?: Json
        }
        Update: {
          id?: string
          created_at?: string
          user_id?: string | null
          user_email?: string
          company_id?: string | null
          action?: string
          resource_type?: string
          resource_id?: string | null
          old_values?: Json | null
          new_values?: Json | null
          ip_address?: string | null
          user_agent?: string | null
          metadata?: Json
        }
      }
      settings: {
        Row: {
          email: string;
          max_contact_limit: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          email: string;
          max_contact_limit?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          email?: string;
          max_contact_limit?: number;
          created_at?: string;
          updated_at?: string;
        };
      }
    }
    Views: {
      // Define your views here
    }
    Functions: {
      // Define your functions here
    }
    Enums: {
      // Define your enums here
    }
  }
}
