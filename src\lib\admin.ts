import { ADMIN_EMAIL } from './config';

export interface AdminStats {
  profiles: number;
  lastBackupTime?: string;
  backupMessage?: string;
}

export interface ProfileData {
  id: string;
  created_at: string;
  updated_at: string | null;
  full_name: string | null;
  email: string | null;
  avatar_url: string | null;
  disabled: boolean;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface AdminApiResponse<T> {
  data: T;
  pagination?: PaginationInfo;
}

/**
 * Check if the current user is an admin
 */
export function isAdmin(userEmail: string | null | undefined): boolean {
  return userEmail === ADMIN_EMAIL;
}

/**
 * Fetch backup status
 */
export async function fetchBackupStatus(): Promise<{ lastBackupTime?: string; message?: string; hasBackup: boolean }> {
  const response = await fetch('/api/admin/backup-status');

  if (!response.ok) {
    throw new Error('Failed to fetch backup status');
  }

  return response.json();
}

/**
 * Fetch admin statistics
 */
export async function fetchAdminStats(): Promise<AdminStats> {
  const [statsResponse, backupStatus] = await Promise.all([
    fetch('/api/admin/stats'),
    fetchBackupStatus().catch(() => ({ lastBackupTime: undefined, message: 'Error fetching backup status', hasBackup: false }))
  ]);

  if (!statsResponse.ok) {
    throw new Error('Failed to fetch admin stats');
  }

  const stats = await statsResponse.json();

  return {
    ...stats,
    lastBackupTime: backupStatus.lastBackupTime,
    backupMessage: backupStatus.message
  };
}

/**
 * Fetch profiles data with pagination
 */
export async function fetchProfilesData(
  page: number = 1,
  limit: number = 50
): Promise<AdminApiResponse<ProfileData[]>> {
  const response = await fetch(`/api/admin/profiles?page=${page}&limit=${limit}`);

  if (!response.ok) {
    throw new Error('Failed to fetch profiles data');
  }

  return response.json();
}





/**
 * Delete a profile and all associated data (admin only)
 */
export async function deleteProfile(profileEmail: string): Promise<void> {
  const response = await fetch('/api/admin/profiles', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ profileEmail }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to delete profile');
  }
}



/**
 * Format timestamp to readable date
 */
export function formatTimestamp(timestamp: string): string {
  return new Date(timestamp).toLocaleString();
}

/**
 * Get profile email display name
 */
export function getProfileDisplayName(profile: string | null, email: string | null): string {
  if (profile) return profile;
  if (email) return email;
  return 'Unknown';
}
