"use client"

import { useState } from "react";
import { LoadingOverlay } from '@mantine/core';
import { useUserCompanyStatus } from "src/hooks/useUserCompanyStatus";
import { FirstTimeLoginFlow } from "../FirstTimeLogin/FirstTimeLoginFlow";
import { CompanyDashboard } from "../CompanyDashboard/CompanyDashboard";


export const SignedIn = () => {
  const { hasCompany, loading } = useUserCompanyStatus();
  const [showFirstTimeFlow, setShowFirstTimeFlow] = useState(false);

  // Show loading while checking company status
  if (loading) {
    return (
      <div style={{ position: 'relative', minHeight: 400 }}>
        <LoadingOverlay visible />
      </div>
    );
  }

  // Show first-time login flow if user has no company
  if (!hasCompany || showFirstTimeFlow) {
    return (
      <FirstTimeLoginFlow
        onComplete={() => setShowFirstTimeFlow(false)}
      />
    );
  }

  // Show company dashboard for users with companies
  return <CompanyDashboard />;
}


