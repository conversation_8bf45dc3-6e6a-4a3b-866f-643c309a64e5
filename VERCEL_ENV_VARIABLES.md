# Environment Variables for Vercel Deployment

## Required Environment Variables

### NextAuth Configuration
```
NEXTAUTH_URL=https://your-domain.vercel.app
NEXTAUTH_SECRET=your-super-secret-key-here-make-it-long-and-random
```

### Supabase Configuration (Required)
```
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

### OAuth Providers (At least one required)
```
# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# GitHub OAuth (Optional)
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Facebook OAuth (Optional)
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret
```

## Optional Environment Variables

### SMTP Configuration (for contact forms and notifications)
```
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-16-character-app-password
CONTACT_EMAIL=<EMAIL>
```

### Admin Configuration
```
ADMIN_EMAIL=<EMAIL>
```

## How to Set Environment Variables in Vercel

1. Go to your Vercel dashboard
2. Select your project
3. Go to Settings → Environment Variables
4. Add each variable with its corresponding value
5. Make sure to set the environment to "Production", "Preview", and "Development" as needed

## Important Notes

- **NEXTAUTH_SECRET**: Generate using `openssl rand -base64 32` or https://generate-secret.vercel.app/32
- **NEXTAUTH_URL**: Must match your actual domain (https://your-domain.vercel.app)
- **Supabase Keys**: Get from your Supabase project dashboard → Settings → API
- **OAuth Credentials**: Configure redirect URIs in respective provider consoles:
  - Google: `https://your-domain.vercel.app/api/auth/callback/google`
  - GitHub: `https://your-domain.vercel.app/api/auth/callback/github`
  - Facebook: `https://your-domain.vercel.app/api/auth/callback/facebook`

## Minimum Required for Basic Functionality

For the app to work at minimum, you need:
1. `NEXTAUTH_URL`
2. `NEXTAUTH_SECRET`
3. `NEXT_PUBLIC_SUPABASE_URL`
4. `NEXT_PUBLIC_SUPABASE_ANON_KEY`
5. At least one OAuth provider (Google recommended)

## Troubleshooting

- If you get authentication errors, check that OAuth redirect URIs are correctly configured
- If database errors occur, verify Supabase URL and keys are correct
- For SMTP issues, ensure Gmail app password is used (not regular password)
