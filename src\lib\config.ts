/**
 * Application Configuration Constants
 *
 * This file contains all application-wide constants and configuration values.
 * Import these constants instead of hardcoding values throughout the application.
 */

//Version
export const APP_VERSION = "1.2";

// Admin Configuration
export const ADMIN_EMAIL = "<EMAIL>";

// Default URLs and Assets
export const DEFAULT_AVATAR_URL = "https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-2.png";

// SMTP Configuration for Contact Us
export const SMTP_CONFIG = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: true, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER || '',
    pass: process.env.SMTP_PASS || ''
  }
};

// Contact Us Configuration
export const CONTACT_EMAIL = process.env.CONTACT_EMAIL || '<EMAIL>';

// AdSense Configuration
export const ENABLE_ADSENSE = true;
export const ADSENSE_CLIENT_ID = 'ca-pub-9660854185566265';