# ODude CRM - Multi-Tenant SaaS Platform

## Overview

ODude CRM is a comprehensive multi-tenant Customer Relationship Management platform built with Next.js 15, TypeScript, and Supabase. The system provides company-specific workspaces with role-based access control, custom field management, team collaboration, and advanced analytics.

## 🚀 Key Features

### 1. Multi-Tenant Architecture
- **Company Isolation**: Complete data separation between companies
- **Subscription Management**: Free, Basic, Premium, and Enterprise plans
- **Resource Limits**: Configurable user and record limits per company
- **Custom Domains**: Optional custom domain support for companies

### 2. Authentication & User Management
- **OAuth Integration**: Google, GitHub, and Facebook sign-in
- **Role-Based Access**: Super Admin, Company Admin, Regular User, and Viewer roles
- **Profile Management**: User profiles with timezone and preference settings
- **Account Security**: Account disable/enable functionality with audit trails

### 3. Company Dashboard System

#### For Company Admins:
- **Overview Dashboard**: Key metrics, quick actions, and company statistics
- **Custom Fields Management**: Create and manage company-specific fields
- **Record Management**: Full CRUD operations with search, filter, and export
- **Team Management**: Invite users, manage permissions, and track activity
- **Analytics & Reports**: Company-level insights with export capabilities

#### For Regular Users:
- **Simplified Dashboard**: Access to records and basic functionality
- **Record Creation**: Create records using company's custom forms
- **Profile Management**: Update personal information and settings

### 4. Dynamic Form System
- **Core Fields**: System-wide fields managed by super admin
- **Custom Fields**: Company-specific fields with multiple types:
  - Text, Number, Email, Phone, Price
  - Date, Dropdown, Checkbox
  - Validation rules and default values
- **Form Builder**: Dynamic form generation combining core and custom fields
- **Field Ordering**: Configurable display order for optimal UX

### 5. Record Management
- **Dynamic Storage**: Flexible JSON-based field data storage
- **Search & Filter**: Advanced search across all field types
- **Export Options**: CSV, Excel (XLSX), and JSON export formats
- **Status Management**: Active, archived, and deleted record states
- **Audit Trail**: Complete change history for all records

### 6. Team Collaboration
- **Email Invitations**: Invite team members with role assignment
- **Permission Control**: Company admin can enable/disable users
- **Role Management**: Assign roles with appropriate permissions
- **Activity Tracking**: Monitor team member login and activity

### 7. Analytics & Reporting
- **Company Metrics**: Record counts, user activity, growth trends
- **Field Usage**: Statistics on custom field utilization
- **Export Reports**: Excel and CSV reports for stakeholders
- **Time-based Analysis**: 7-day, 30-day, 90-day, and yearly views

### 8. Security & Compliance
- **Tenant Isolation**: Strict data separation between companies
- **Audit Logging**: Complete audit trail for all system actions
- **Role-based Access**: Granular permission system
- **Data Validation**: Server-side validation for all inputs
- **GDPR Ready**: User data management and deletion capabilities

## 🏗️ Technical Architecture

### Frontend Stack
- **Next.js 15**: App Router with Server Components
- **TypeScript**: Full type safety throughout the application
- **Mantine UI**: Modern React component library
- **NextAuth.js**: Authentication with multiple providers
- **React Hooks**: Custom hooks for state management

### Backend Stack
- **Supabase**: PostgreSQL database with real-time capabilities
- **API Routes**: RESTful API with proper error handling
- **Server Actions**: Server-side form handling and validation
- **Audit System**: Comprehensive logging for compliance

### Database Schema
- **Companies**: Multi-tenant company management
- **Profiles**: User accounts with company associations
- **Core Fields**: System-wide field definitions
- **Custom Fields**: Company-specific field definitions
- **Records**: Dynamic record storage with JSON fields
- **Company Invitations**: Team invitation management
- **Audit Logs**: Complete system activity tracking

## 📋 User Roles & Permissions

### Super Admin
- Manage all companies and users
- Create and modify core fields
- Access system-wide analytics
- Company subscription management

### Company Admin
- Full access to company data and settings
- Manage custom fields and forms
- Invite and manage team members
- Access company analytics and reports
- Export company data

### Regular User
- Create and manage records
- View company records (with restrictions)
- Update personal profile
- Limited dashboard access

### Viewer
- Read-only access to company records
- View analytics and reports
- Cannot create or modify data

## 🚦 Getting Started

### Prerequisites
- Node.js 18+
- Supabase account
- OAuth provider credentials (Google, GitHub, Facebook)

### Installation

1. **Clone and Install**
```bash
git clone <repository-url>
cd odude-crm
npm install
```

2. **Environment Setup**
Create `.env.local`:
```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSKEY=your-secure-passkey
```

3. **Database Setup**
```bash
# Run the database setup script in Supabase SQL Editor
# Execute: scripts/fresh-database-setup.sql
```

4. **Start Development Server**
```bash
npm run dev
```

### First-Time Setup

1. **Access the Application**: Navigate to `http://localhost:3000`
2. **Sign In**: Use OAuth to create your account
3. **Create Company**: First-time users can create a new company
4. **Invite Team**: Company admins can invite team members
5. **Configure Fields**: Set up custom fields for your company
6. **Start Creating Records**: Begin managing your data

## 📊 Usage Guide

### For Company Admins

1. **Dashboard Overview**
   - Monitor key metrics and company statistics
   - Quick access to common actions
   - View recent activity and trends

2. **Custom Fields Management**
   - Create fields specific to your business needs
   - Configure validation rules and default values
   - Organize field display order

3. **Team Management**
   - Send email invitations to team members
   - Assign roles and permissions
   - Monitor team activity and login history

4. **Record Management**
   - Create, edit, and delete records
   - Search and filter across all fields
   - Export data in multiple formats

5. **Analytics & Reports**
   - View company performance metrics
   - Analyze field usage and trends
   - Export reports for stakeholders

### For Regular Users

1. **Record Creation**
   - Use dynamic forms with company fields
   - Fill required and optional information
   - Save and manage personal records

2. **Record Management**
   - View and edit assigned records
   - Search through accessible data
   - Update record status and information

3. **Profile Management**
   - Update personal information
   - Set timezone preferences
   - Manage account settings

## 🔧 API Endpoints

### Authentication
- `GET /api/user/profile` - Get current user profile
- `PATCH /api/user/profile` - Update user profile

### Company Management
- `POST /api/company/create` - Create new company
- `GET /api/company/create/check-slug` - Check slug availability

### Team Management
- `GET /api/company/team` - Get team members
- `PATCH /api/company/team/[id]` - Update team member

### Invitations
- `GET /api/company/invitations` - Get company invitations
- `POST /api/company/invitations` - Send invitation
- `PATCH /api/company/invitations/[id]` - Update invitation
- `POST /api/invitations/accept` - Accept invitation
- `GET /api/user/invitations` - Get user invitations

### Custom Fields
- `GET /api/company/custom-fields` - Get custom fields
- `POST /api/company/custom-fields` - Create custom field
- `PUT /api/company/custom-fields/[id]` - Update custom field
- `DELETE /api/company/custom-fields/[id]` - Delete custom field

### Records
- `GET /api/records` - Get records with pagination
- `POST /api/records` - Create new record
- `PUT /api/records/[id]` - Update record
- `DELETE /api/records/[id]` - Delete record

### Forms
- `GET /api/forms/fields` - Get form fields (core + custom)

## 🛡️ Security Features

- **Data Isolation**: Complete separation between companies
- **Role-based Access**: Granular permission system
- **Audit Logging**: All actions tracked and logged
- **Input Validation**: Server-side validation for all inputs
- **Session Management**: Secure session handling with NextAuth
- **SQL Injection Protection**: Parameterized queries via Supabase

## 📈 Scalability

- **Multi-tenant Architecture**: Efficient resource sharing
- **Database Indexing**: Optimized queries for performance
- **Caching Strategy**: Client-side and server-side caching
- **API Rate Limiting**: Protection against abuse
- **Horizontal Scaling**: Designed for cloud deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation for common solutions

---

**ODude CRM** - Empowering businesses with intelligent customer relationship management.
