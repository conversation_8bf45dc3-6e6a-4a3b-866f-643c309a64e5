'use client';

import { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  Text,
  Group,
  Button,
  TextInput,
  Select,
  Paper,
  Badge,
  ActionIcon,
  Menu,
  Alert,
  LoadingOverlay,
  Pagination,
  Table,
  Tooltip,
  Modal,
  Checkbox,
  SimpleGrid,
  Avatar,
  Progress,
} from '@mantine/core';
import {
  IconSearch,
  IconFilter,
  IconDownload,
  IconEdit,
  IconTrash,
  IconEye,
  IconEyeOff,
  IconDots,
  IconAlertCircle,
  IconCheck,
  IconX,
  IconUsers,
  IconBuilding,
  IconShield,
  IconClock,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';

interface EnhancedProfile {
  id: string;
  created_at: string;
  updated_at: string | null;
  full_name: string | null;
  email: string | null;
  avatar_url: string | null;
  disabled: boolean;
  company_id: string | null;
  role: string;
  is_company_admin: boolean;
  last_login_at: string | null;
  timezone: string;
  record_count: number;
  company_name: string | null;
  company_slug: string | null;
  subscription_plan: string | null;
  subscription_status: string | null;
}

interface ProfileFilters {
  search: string;
  companyId: string;
  role: string;
  isCompanyAdmin: string;
  disabled: string;
  page: number;
  limit: number;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface ProfileStats {
  total: number;
  active: number;
  disabled: number;
  companyAdmins: number;
  withoutCompany: number;
  byRole: Record<string, number>;
}

export function EnhancedProfileManagement() {
  const [profiles, setProfiles] = useState<EnhancedProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<ProfileStats | null>(null);
  const [selectedProfiles, setSelectedProfiles] = useState<Set<string>>(new Set());
  const [filters, setFilters] = useState<ProfileFilters>({
    search: '',
    companyId: '',
    role: '',
    isCompanyAdmin: '',
    disabled: '',
    page: 1,
    limit: 20,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });
  const [bulkActionOpened, { open: openBulkAction, close: closeBulkAction }] = useDisclosure(false);
  const [bulkAction, setBulkAction] = useState<string>('');
  const [bulkActionData, setBulkActionData] = useState<any>({});

  // Individual profile action states
  const [viewModalOpened, { open: openViewModal, close: closeViewModal }] = useDisclosure(false);
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false);
  const [selectedProfile, setSelectedProfile] = useState<EnhancedProfile | null>(null);
  const [actionLoading, setActionLoading] = useState(false);

  // Load profiles
  const loadProfiles = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value.toString());
      });

      const response = await fetch(`/api/admin/profiles/enhanced?${params.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load profiles');
      }

      const data = await response.json();
      setProfiles(data.data);
      setPagination(data.pagination);
      setStats(data.stats);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load profiles';
      setError(errorMessage);
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconX size={16} />
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProfiles();
  }, [filters]);

  // Handle search
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }));
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof ProfileFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // Handle profile selection
  const handleProfileSelect = (profileId: string, checked: boolean) => {
    const newSelected = new Set(selectedProfiles);
    if (checked) {
      newSelected.add(profileId);
    } else {
      newSelected.delete(profileId);
    }
    setSelectedProfiles(newSelected);
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProfiles(new Set(profiles.map(p => p.id)));
    } else {
      setSelectedProfiles(new Set());
    }
  };

  // Handle bulk action
  const handleBulkAction = (action: string, data?: any) => {
    setBulkAction(action);
    setBulkActionData(data || {});
    openBulkAction();
  };

  // Confirm bulk action
  const confirmBulkAction = async () => {
    try {
      const response = await fetch('/api/admin/profiles/enhanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: bulkAction,
          profileIds: Array.from(selectedProfiles),
          data: bulkActionData
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Bulk action failed');
      }

      const result = await response.json();
      
      closeBulkAction();
      setSelectedProfiles(new Set());
      loadProfiles();
      
      notifications.show({
        title: 'Success',
        message: result.message,
        color: 'green',
        icon: <IconCheck size={16} />
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Bulk action failed';
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconX size={16} />
      });
    }
  };

  // Handle export
  const handleExport = async () => {
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value && key !== 'page' && key !== 'limit') {
          params.append(key, value.toString());
        }
      });
      params.append('format', 'csv');
      params.append('limit', '10000');

      const response = await fetch(`/api/admin/reports?type=users&${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Export failed');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `profiles-export-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      notifications.show({
        title: 'Success',
        message: 'Profiles exported successfully',
        color: 'green',
        icon: <IconCheck size={16} />
      });
    } catch (err) {
      notifications.show({
        title: 'Error',
        message: 'Failed to export profiles',
        color: 'red',
        icon: <IconX size={16} />
      });
    }
  };

  // Individual profile action handlers
  const handleViewProfile = (profile: EnhancedProfile) => {
    setSelectedProfile(profile);
    openViewModal();
  };

  const handleEditProfile = (profile: EnhancedProfile) => {
    setSelectedProfile(profile);
    openEditModal();
  };

  const handleToggleProfileStatus = async (profile: EnhancedProfile) => {
    try {
      setActionLoading(true);
      const response = await fetch('/api/admin/profiles/toggle-disable', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          profileId: profile.id,
          disabled: !profile.disabled
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update profile status');
      }

      notifications.show({
        title: 'Success',
        message: `Profile ${profile.disabled ? 'enabled' : 'disabled'} successfully`,
        color: 'green',
        icon: <IconCheck size={16} />
      });

      // Reload profiles to reflect changes
      loadProfiles();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to update profile status',
        color: 'red',
        icon: <IconX size={16} />
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteProfile = async (profile: EnhancedProfile) => {
    if (!confirm(`Are you sure you want to delete ${profile.full_name || profile.email}? This action cannot be undone.`)) {
      return;
    }

    try {
      setActionLoading(true);
      const response = await fetch('/api/admin/profiles', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          profileId: profile.id
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete profile');
      }

      notifications.show({
        title: 'Success',
        message: 'Profile deleted successfully',
        color: 'green',
        icon: <IconCheck size={16} />
      });

      // Reload profiles to reflect changes
      loadProfiles();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to delete profile',
        color: 'red',
        icon: <IconX size={16} />
      });
    } finally {
      setActionLoading(false);
    }
  };

  const formatLastLogin = (lastLogin: string | null) => {
    if (!lastLogin) return 'Never';
    const date = new Date(lastLogin);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  return (
    <Stack gap="md">
      {/* Header */}
      <Group justify="space-between">
        <div>
          <Title order={2}>Profile Management</Title>
          <Text c="dimmed" size="sm">
            Manage user profiles with advanced filtering and bulk operations
          </Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />} onClick={handleExport}>
            Export
          </Button>
          {selectedProfiles.size > 0 && (
            <Menu shadow="md" width={200}>
              <Menu.Target>
                <Button leftSection={<IconDots size={16} />}>
                  Bulk Actions ({selectedProfiles.size})
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item onClick={() => handleBulkAction('bulk_disable')}>
                  Disable Selected
                </Menu.Item>
                <Menu.Item onClick={() => handleBulkAction('bulk_enable')}>
                  Enable Selected
                </Menu.Item>
                <Menu.Divider />
                <Menu.Item onClick={() => handleBulkAction('bulk_update_role', { role: 'user' })}>
                  Set Role to User
                </Menu.Item>
                <Menu.Item onClick={() => handleBulkAction('bulk_update_role', { role: 'admin' })}>
                  Set Role to Admin
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          )}
        </Group>
      </Group>

      {/* Statistics Cards */}
      {stats && (
        <SimpleGrid cols={{ base: 1, sm: 2, md: 5 }} spacing="md">
          <Paper withBorder p="md" radius="md">
            <Group justify="apart">
              <div>
                <Text c="dimmed" tt="uppercase" fw={700} fz="xs">Total</Text>
                <Text fw={700} fz="xl">{stats.total}</Text>
              </div>
              <IconUsers size={24} color="#228be6" />
            </Group>
          </Paper>
          <Paper withBorder p="md" radius="md">
            <Group justify="apart">
              <div>
                <Text c="dimmed" tt="uppercase" fw={700} fz="xs">Active</Text>
                <Text fw={700} fz="xl">{stats.active}</Text>
              </div>
              <IconCheck size={24} color="#40c057" />
            </Group>
          </Paper>
          <Paper withBorder p="md" radius="md">
            <Group justify="apart">
              <div>
                <Text c="dimmed" tt="uppercase" fw={700} fz="xs">Disabled</Text>
                <Text fw={700} fz="xl">{stats.disabled}</Text>
              </div>
              <IconEyeOff size={24} color="#fd7e14" />
            </Group>
          </Paper>
          <Paper withBorder p="md" radius="md">
            <Group justify="apart">
              <div>
                <Text c="dimmed" tt="uppercase" fw={700} fz="xs">Admins</Text>
                <Text fw={700} fz="xl">{stats.companyAdmins}</Text>
              </div>
              <IconShield size={24} color="#e64980" />
            </Group>
          </Paper>
          <Paper withBorder p="md" radius="md">
            <Group justify="apart">
              <div>
                <Text c="dimmed" tt="uppercase" fw={700} fz="xs">No Company</Text>
                <Text fw={700} fz="xl">{stats.withoutCompany}</Text>
              </div>
              <IconBuilding size={24} color="#868e96" />
            </Group>
          </Paper>
        </SimpleGrid>
      )}

      {/* Filters */}
      <Paper p="md" withBorder>
        <Group>
          <TextInput
            placeholder="Search profiles..."
            leftSection={<IconSearch size={16} />}
            value={filters.search}
            onChange={(e) => handleSearch(e.target.value)}
            style={{ flex: 1 }}
          />
          <Select
            placeholder="Role"
            data={[
              { value: '', label: 'All Roles' },
              { value: 'admin', label: 'Admin' },
              { value: 'user', label: 'User' },
              { value: 'viewer', label: 'Viewer' }
            ]}
            value={filters.role}
            onChange={(value) => handleFilterChange('role', value || '')}
            clearable
          />
          <Select
            placeholder="Company Admin"
            data={[
              { value: '', label: 'All' },
              { value: 'true', label: 'Company Admin' },
              { value: 'false', label: 'Regular User' }
            ]}
            value={filters.isCompanyAdmin}
            onChange={(value) => handleFilterChange('isCompanyAdmin', value || '')}
            clearable
          />
          <Select
            placeholder="Status"
            data={[
              { value: '', label: 'All Status' },
              { value: 'false', label: 'Active' },
              { value: 'true', label: 'Disabled' }
            ]}
            value={filters.disabled}
            onChange={(value) => handleFilterChange('disabled', value || '')}
            clearable
          />
        </Group>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
      )}

      {/* Profiles Table */}
      <Paper withBorder style={{ position: 'relative' }}>
        <LoadingOverlay visible={loading} />
        
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>
                <Checkbox
                  checked={selectedProfiles.size === profiles.length && profiles.length > 0}
                  indeterminate={selectedProfiles.size > 0 && selectedProfiles.size < profiles.length}
                  onChange={(e) => handleSelectAll(e.currentTarget.checked)}
                />
              </Table.Th>
              <Table.Th>User</Table.Th>
              <Table.Th>Company</Table.Th>
              <Table.Th>Role</Table.Th>
              <Table.Th>Records</Table.Th>
              <Table.Th>Last Login</Table.Th>
              <Table.Th>Status</Table.Th>
              <Table.Th>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {profiles.map((profile) => (
              <Table.Tr key={profile.id}>
                <Table.Td>
                  <Checkbox
                    checked={selectedProfiles.has(profile.id)}
                    onChange={(e) => handleProfileSelect(profile.id, e.currentTarget.checked)}
                  />
                </Table.Td>
                <Table.Td>
                  <Group gap="sm">
                    <Avatar
                      src={profile.avatar_url}
                      size="sm"
                      radius="xl"
                    />
                    <div>
                      <Text fw={500}>{profile.full_name || 'No Name'}</Text>
                      <Text size="xs" c="dimmed">{profile.email}</Text>
                    </div>
                  </Group>
                </Table.Td>
                <Table.Td>
                  {profile.company_name ? (
                    <div>
                      <Text size="sm">{profile.company_name}</Text>
                      <Badge size="xs" variant="light" color={
                        profile.subscription_plan === 'enterprise' ? 'violet' :
                        profile.subscription_plan === 'premium' ? 'green' :
                        profile.subscription_plan === 'basic' ? 'blue' : 'gray'
                      }>
                        {profile.subscription_plan}
                      </Badge>
                    </div>
                  ) : (
                    <Text c="dimmed" size="sm">No Company</Text>
                  )}
                </Table.Td>
                <Table.Td>
                  <Group gap="xs">
                    <Badge variant="light" color={profile.role === 'admin' ? 'red' : 'blue'}>
                      {profile.role}
                    </Badge>
                    {profile.is_company_admin && (
                      <Badge size="xs" color="orange">Admin</Badge>
                    )}
                  </Group>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">{profile.record_count}</Text>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">{formatLastLogin(profile.last_login_at)}</Text>
                </Table.Td>
                <Table.Td>
                  <Badge
                    variant="light"
                    color={profile.disabled ? 'red' : 'green'}
                  >
                    {profile.disabled ? 'Disabled' : 'Active'}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  <Group gap="xs">
                    <Tooltip label="View Details">
                      <ActionIcon
                        variant="subtle"
                        color="blue"
                        onClick={() => handleViewProfile(profile)}
                        loading={actionLoading}
                      >
                        <IconEye size={16} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="Edit">
                      <ActionIcon
                        variant="subtle"
                        color="orange"
                        onClick={() => handleEditProfile(profile)}
                        loading={actionLoading}
                      >
                        <IconEdit size={16} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label={profile.disabled ? "Enable" : "Disable"}>
                      <ActionIcon
                        variant="subtle"
                        color={profile.disabled ? "green" : "red"}
                        onClick={() => handleToggleProfileStatus(profile)}
                        loading={actionLoading}
                      >
                        {profile.disabled ? <IconEye size={16} /> : <IconEyeOff size={16} />}
                      </ActionIcon>
                    </Tooltip>
                    <Menu shadow="md" width={200}>
                      <Menu.Target>
                        <ActionIcon variant="subtle" color="gray">
                          <IconDots size={16} />
                        </ActionIcon>
                      </Menu.Target>
                      <Menu.Dropdown>
                        <Menu.Item
                          leftSection={<IconTrash size={14} />}
                          color="red"
                          onClick={() => handleDeleteProfile(profile)}
                        >
                          Delete Profile
                        </Menu.Item>
                      </Menu.Dropdown>
                    </Menu>
                  </Group>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>

        {profiles.length === 0 && !loading && (
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <Text c="dimmed">No profiles found</Text>
          </div>
        )}
      </Paper>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <Group justify="center">
          <Pagination
            value={pagination.page}
            onChange={handlePageChange}
            total={pagination.totalPages}
          />
        </Group>
      )}

      {/* View Profile Modal */}
      <Modal
        opened={viewModalOpened}
        onClose={closeViewModal}
        title="Profile Details"
        size="lg"
      >
        {selectedProfile && (
          <Stack gap="md">
            <Group>
              <Avatar
                src={selectedProfile.avatar_url}
                size="lg"
                radius="xl"
              />
              <div>
                <Text fw={500} size="lg">{selectedProfile.full_name || 'No Name'}</Text>
                <Text c="dimmed">{selectedProfile.email}</Text>
                <Badge
                  variant="light"
                  color={selectedProfile.disabled ? 'red' : 'green'}
                  mt="xs"
                >
                  {selectedProfile.disabled ? 'Disabled' : 'Active'}
                </Badge>
              </div>
            </Group>

            <SimpleGrid cols={2} spacing="md">
              <div>
                <Text fw={500} size="sm" mb="xs">Company</Text>
                <Text size="sm" c="dimmed">
                  {selectedProfile.company_name || 'No Company'}
                </Text>
                {selectedProfile.company_slug && (
                  <Text size="xs" c="dimmed">
                    Slug: {selectedProfile.company_slug}
                  </Text>
                )}
              </div>
              <div>
                <Text fw={500} size="sm" mb="xs">Role</Text>
                <Group gap="xs">
                  <Badge variant="outline" size="sm">
                    {selectedProfile.role}
                  </Badge>
                  {selectedProfile.is_company_admin && (
                    <Badge variant="light" color="blue" size="sm">
                      Company Admin
                    </Badge>
                  )}
                </Group>
              </div>
              <div>
                <Text fw={500} size="sm" mb="xs">Records</Text>
                <Text size="sm" c="dimmed">
                  {selectedProfile.record_count} records
                </Text>
              </div>
              <div>
                <Text fw={500} size="sm" mb="xs">Last Login</Text>
                <Text size="sm" c="dimmed">
                  {formatLastLogin(selectedProfile.last_login_at)}
                </Text>
              </div>
              <div>
                <Text fw={500} size="sm" mb="xs">Timezone</Text>
                <Text size="sm" c="dimmed">
                  {selectedProfile.timezone}
                </Text>
              </div>
              <div>
                <Text fw={500} size="sm" mb="xs">Created</Text>
                <Text size="sm" c="dimmed">
                  {new Date(selectedProfile.created_at).toLocaleString()}
                </Text>
              </div>
            </SimpleGrid>

            {selectedProfile.subscription_plan && (
              <div>
                <Text fw={500} size="sm" mb="xs">Subscription</Text>
                <Group gap="xs">
                  <Badge
                    variant="light"
                    color={
                      selectedProfile.subscription_plan === 'enterprise' ? 'violet' :
                      selectedProfile.subscription_plan === 'premium' ? 'green' :
                      selectedProfile.subscription_plan === 'basic' ? 'blue' : 'gray'
                    }
                  >
                    {selectedProfile.subscription_plan}
                  </Badge>
                  <Badge
                    variant="outline"
                    color={
                      selectedProfile.subscription_status === 'active' ? 'green' :
                      selectedProfile.subscription_status === 'suspended' ? 'yellow' : 'red'
                    }
                  >
                    {selectedProfile.subscription_status}
                  </Badge>
                </Group>
              </div>
            )}
          </Stack>
        )}
      </Modal>

      {/* Edit Profile Modal */}
      <Modal
        opened={editModalOpened}
        onClose={closeEditModal}
        title="Edit Profile"
        size="md"
      >
        {selectedProfile && (
          <Stack gap="md">
            <Alert color="blue" icon={<IconAlertCircle size={16} />}>
              Profile editing functionality will be implemented here. For now, use the toggle buttons to enable/disable profiles.
            </Alert>
            <Group justify="flex-end">
              <Button variant="outline" onClick={closeEditModal}>
                Close
              </Button>
            </Group>
          </Stack>
        )}
      </Modal>

      {/* Bulk Action Confirmation Modal */}
      <Modal
        opened={bulkActionOpened}
        onClose={closeBulkAction}
        title="Confirm Bulk Action"
        size="sm"
      >
        <Stack>
          <Text>
            Are you sure you want to perform this action on {selectedProfiles.size} selected profile(s)?
          </Text>
          <Group justify="flex-end">
            <Button variant="default" onClick={closeBulkAction}>
              Cancel
            </Button>
            <Button color="red" onClick={confirmBulkAction}>
              Confirm
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
}
