export type InvitationStatus = 'pending' | 'accepted' | 'declined' | 'expired';
export type InvitationRole = 'user' | 'admin' | 'viewer';

export interface CompanyInvitation {
  id: string;
  created_at: string;
  updated_at: string | null;
  company_id: string;
  invited_by: string;
  email: string;
  role: InvitationRole;
  is_company_admin: boolean;
  status: InvitationStatus;
  token: string;
  expires_at: string;
  accepted_at: string | null;
  accepted_by: string | null;
  metadata: Record<string, any>;
  
  // Joined data
  company?: {
    id: string;
    name: string;
    logo_url: string | null;
    subscription_plan: string;
  };
  invited_by_user?: {
    id: string;
    full_name: string | null;
    email: string | null;
  };
  accepted_by_user?: {
    id: string;
    full_name: string | null;
    email: string | null;
  };
}

export interface InvitationFormData {
  email: string;
  role: InvitationRole;
  is_company_admin: boolean;
  message?: string;
}

export interface InvitationsResponse {
  data: CompanyInvitation[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface InvitationFilters {
  status?: InvitationStatus | '';
  role?: InvitationRole | '';
  search?: string;
  page?: number;
  limit?: number;
}

export interface AcceptInvitationData {
  token: string;
  user_id?: string;
}

export interface InvitationEmailData {
  to: string;
  companyName: string;
  inviterName: string;
  inviterEmail: string;
  role: string;
  acceptUrl: string;
  expiresAt: string;
  message?: string;
}

export const INVITATION_ROLE_LABELS: Record<InvitationRole, string> = {
  user: 'Regular User',
  admin: 'Admin',
  viewer: 'Viewer'
};

export const INVITATION_STATUS_LABELS: Record<InvitationStatus, string> = {
  pending: 'Pending',
  accepted: 'Accepted',
  declined: 'Declined',
  expired: 'Expired'
};

export function isInvitationExpired(invitation: CompanyInvitation): boolean {
  return new Date(invitation.expires_at) < new Date();
}

export function getInvitationStatusColor(status: InvitationStatus): string {
  switch (status) {
    case 'pending':
      return 'yellow';
    case 'accepted':
      return 'green';
    case 'declined':
      return 'red';
    case 'expired':
      return 'gray';
    default:
      return 'gray';
  }
}

export function getRoleColor(role: InvitationRole): string {
  switch (role) {
    case 'admin':
      return 'red';
    case 'user':
      return 'blue';
    case 'viewer':
      return 'gray';
    default:
      return 'gray';
  }
}
