import { NextRequest } from 'next/server';
import { auth } from 'auth';
import { ADMIN_EMAIL } from './config';
import { getSupabaseClient } from './supabase';
import { createAuditLog } from './auditLog';

export interface AdminAuthResult {
  isAuthorized: boolean;
  isSuperAdmin: boolean;
  session: any;
  profile?: any;
  error: string | null;
  status: number;
}

/**
 * Server-side admin authentication check
 * This function checks both the user session and database profile
 * Supports both super admin and company admin access
 */
export async function verifyAdminAuth(request?: NextRequest): Promise<AdminAuthResult> {
  try {
    // Get the current session
    const session = await auth();

    if (!session?.user?.email) {
      return {
        isAuthorized: false,
        isSuperAdmin: false,
        session: null,
        error: 'No authenticated session',
        status: 401
      };
    }

    const userEmail = session.user.email;
    const supabase = getSupabaseClient();

    // Check if user is super admin by email (legacy check)
    const isSuperAdminByEmail = userEmail === ADMIN_EMAIL;

    // Get user profile from database
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select(`
        id,
        email,
        full_name,
        role,
        is_company_admin,
        company_id,
        disabled,
        companies:company_id(
          id,
          name,
          subscription_status
        )
      `)
      .eq('email', userEmail)
      .single();

    // If profile doesn't exist but user is super admin by email, allow access
    if (profileError && isSuperAdminByEmail) {
      // Log admin access
      if (request) {
        await createAuditLog({
          user_email: userEmail,
          action: 'ACCESS',
          resource_type: 'admin_dashboard',
          ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
          user_agent: request.headers.get('user-agent'),
          metadata: {
            is_super_admin: true,
            access_method: 'email_based',
            endpoint: request.url
          }
        });
      }

      return {
        isAuthorized: true,
        isSuperAdmin: true,
        session,
        error: null,
        status: 200
      };
    }

    if (profileError || !profile) {
      return {
        isAuthorized: false,
        isSuperAdmin: false,
        session,
        error: 'User profile not found',
        status: 404
      };
    }

    // Check if user is disabled
    if (profile.disabled) {
      return {
        isAuthorized: false,
        isSuperAdmin: false,
        session,
        profile,
        error: 'User account is disabled',
        status: 403
      };
    }

    // Check if user has admin role
    const isAdmin = profile.role === 'admin' || isSuperAdminByEmail;
    const isSuperAdmin = isSuperAdminByEmail || (isAdmin && !profile.company_id);

    if (!isAdmin) {
      return {
        isAuthorized: false,
        isSuperAdmin: false,
        session,
        profile,
        error: 'Admin access required',
        status: 403
      };
    }

    // Log admin access
    if (request) {
      await createAuditLog({
        user_id: profile.id,
        user_email: userEmail,
        company_id: profile.company_id,
        action: 'ACCESS',
        resource_type: 'admin_dashboard',
        ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        user_agent: request.headers.get('user-agent'),
        metadata: {
          is_super_admin: isSuperAdmin,
          access_method: 'profile_based',
          endpoint: request.url
        }
      });
    }

    return {
      isAuthorized: true,
      isSuperAdmin,
      session,
      profile,
      error: null,
      status: 200
    };
  } catch (error) {
    console.error('Admin auth verification error:', error);
    return {
      isAuthorized: false,
      isSuperAdmin: false,
      session: null,
      error: 'Authentication failed',
      status: 500
    };
  }
}

/**
 * Middleware function to protect admin API routes
 * Usage: const authResult = await requireAdminAuth();
 * if (!authResult.isAuthorized) return NextResponse.json({ error: authResult.error }, { status: authResult.status });
 */
export async function requireAdminAuth(request?: NextRequest): Promise<AdminAuthResult> {
  return await verifyAdminAuth(request);
}
