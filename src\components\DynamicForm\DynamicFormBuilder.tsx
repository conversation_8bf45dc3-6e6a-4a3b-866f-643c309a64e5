'use client';

import { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  Text,
  Group,
  Button,
  TextInput,
  Textarea,
  NumberInput,
  Select,
  Checkbox,
  DateInput,
  Paper,
  Alert,
  LoadingOverlay,
  Divider,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconCheck,
  IconX,
  IconAlertCircle,
} from '@tabler/icons-react';
import { CoreField, validateFieldValue } from 'src/types/coreFields';

interface CustomField {
  id: string;
  name: string;
  field_key: string;
  field_type: string;
  description: string | null;
  is_required: boolean;
  is_active: boolean;
  default_value: string | null;
  validation_rules: Record<string, any>;
  dropdown_options: string[] | null;
  display_order: number;
}

interface DynamicFormBuilderProps {
  coreFields: CoreField[];
  customFields: CustomField[];
  initialData?: Record<string, any>;
  onSubmit: (data: { coreFieldData: Record<string, any>; customFieldData: Record<string, any> }) => Promise<void>;
  onCancel?: () => void;
  submitLabel?: string;
  title?: string;
  description?: string;
}

export function DynamicFormBuilder({
  coreFields,
  customFields,
  initialData,
  onSubmit,
  onCancel,
  submitLabel = 'Submit',
  title = 'Form',
  description
}: DynamicFormBuilderProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Combine and sort all fields
  const allFields = [
    ...coreFields.filter(field => field.is_active),
    ...customFields.filter(field => field.is_active)
  ].sort((a, b) => a.display_order - b.display_order);

  // Initialize form with default values
  const form = useForm({
    initialValues: {
      ...allFields.reduce((acc, field) => {
        const fieldKey = field.field_key;
        const initialValue = initialData?.[fieldKey] || field.default_value || getDefaultValueForFieldType(field.field_type);
        acc[fieldKey] = initialValue;
        return acc;
      }, {} as Record<string, any>)
    },
    validate: (values) => {
      const errors: Record<string, string> = {};
      
      allFields.forEach(field => {
        const value = values[field.field_key];
        const validation = validateFieldValue(value, field as CoreField);
        
        if (!validation.isValid && validation.error) {
          errors[field.field_key] = validation.error;
        }
      });
      
      return errors;
    }
  });

  // Handle form submission
  const handleSubmit = async (values: Record<string, any>) => {
    try {
      setLoading(true);
      setError(null);

      // Separate core and custom field data
      const coreFieldData: Record<string, any> = {};
      const customFieldData: Record<string, any> = {};

      Object.entries(values).forEach(([key, value]) => {
        const isCoreField = coreFields.some(field => field.field_key === key);
        
        if (isCoreField) {
          coreFieldData[key] = value;
        } else {
          customFieldData[key] = value;
        }
      });

      await onSubmit({ coreFieldData, customFieldData });

      notifications.show({
        title: 'Success',
        message: 'Form submitted successfully',
        color: 'green',
        icon: <IconCheck size={16} />
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to submit form';
      setError(errorMessage);
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconX size={16} />
      });
    } finally {
      setLoading(false);
    }
  };

  // Render field based on type
  const renderField = (field: CoreField | CustomField) => {
    const fieldProps = {
      key: field.field_key,
      label: field.name,
      description: field.description || undefined,
      required: field.is_required,
      ...form.getInputProps(field.field_key)
    };

    switch (field.field_type) {
      case 'text':
        return <TextInput {...fieldProps} />;

      case 'email':
        return <TextInput {...fieldProps} type="email" />;

      case 'phone':
        return <TextInput {...fieldProps} type="tel" />;

      case 'number':
      case 'price':
        const numberProps = {
          ...fieldProps,
          min: field.validation_rules?.min,
          max: field.validation_rules?.max,
          precision: field.field_type === 'price' ? 2 : (field.validation_rules?.decimal ? 2 : 0)
        };
        if (field.field_type === 'price') {
          numberProps.leftSection = '$';
        }
        return <NumberInput {...numberProps} />;

      case 'dropdown':
        return (
          <Select
            {...fieldProps}
            data={field.dropdown_options?.map(option => ({ value: option, label: option })) || []}
            clearable={!field.is_required}
          />
        );

      case 'checkbox':
        return (
          <Checkbox
            {...fieldProps}
            checked={fieldProps.value || false}
            onChange={(event) => form.setFieldValue(field.field_key, event.currentTarget.checked)}
          />
        );

      case 'date':
        return (
          <DateInput
            {...fieldProps}
            value={fieldProps.value ? new Date(fieldProps.value) : null}
            onChange={(date) => form.setFieldValue(field.field_key, date?.toISOString().split('T')[0] || '')}
          />
        );

      default:
        return <Textarea {...fieldProps} />;
    }
  };

  // Group fields by type for better organization
  const coreFieldsToRender = allFields.filter(field => 
    coreFields.some(cf => cf.field_key === field.field_key)
  );
  const customFieldsToRender = allFields.filter(field => 
    customFields.some(cf => cf.field_key === field.field_key)
  );

  return (
    <Paper p="md" withBorder style={{ position: 'relative' }}>
      <LoadingOverlay visible={loading} />
      
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          {/* Header */}
          <div>
            <Title order={3}>{title}</Title>
            {description && (
              <Text c="dimmed" size="sm" mt="xs">
                {description}
              </Text>
            )}
          </div>

          {/* Error Alert */}
          {error && (
            <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
              {error}
            </Alert>
          )}

          {/* Core Fields Section */}
          {coreFieldsToRender.length > 0 && (
            <>
              <Divider label="Required Information" labelPosition="left" />
              <Stack gap="md">
                {coreFieldsToRender.map(renderField)}
              </Stack>
            </>
          )}

          {/* Custom Fields Section */}
          {customFieldsToRender.length > 0 && (
            <>
              <Divider label="Additional Information" labelPosition="left" />
              <Stack gap="md">
                {customFieldsToRender.map(renderField)}
              </Stack>
            </>
          )}

          {/* No Fields Message */}
          {allFields.length === 0 && (
            <Alert icon={<IconAlertCircle size={16} />} title="No Fields Available" color="yellow">
              No active fields are configured for this form.
            </Alert>
          )}

          {/* Form Actions */}
          {allFields.length > 0 && (
            <Group justify="flex-end" mt="md">
              {onCancel && (
                <Button variant="default" onClick={onCancel} disabled={loading}>
                  Cancel
                </Button>
              )}
              <Button type="submit" loading={loading}>
                {submitLabel}
              </Button>
            </Group>
          )}
        </Stack>
      </form>
    </Paper>
  );
}

// Helper function to get default value for field type
function getDefaultValueForFieldType(fieldType: string): any {
  switch (fieldType) {
    case 'checkbox':
      return false;
    case 'number':
    case 'price':
      return 0;
    case 'date':
      return '';
    default:
      return '';
  }
}
