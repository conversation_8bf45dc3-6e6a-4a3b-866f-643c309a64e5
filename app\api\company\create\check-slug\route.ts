import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';

/**
 * GET /api/company/create/check-slug?slug=xxx
 * Check if a company slug is available
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const slug = searchParams.get('slug');

    if (!slug) {
      return NextResponse.json({ error: 'Slug parameter is required' }, { status: 400 });
    }

    // Validate slug format
    const slugRegex = /^[a-z0-9-_]+$/;
    if (!slugRegex.test(slug)) {
      return NextResponse.json({ 
        available: false,
        error: 'Slug must contain only lowercase letters, numbers, hyphens, and underscores' 
      });
    }

    const supabase = getSupabaseClient();

    // Check if slug exists
    const { data: existingSlug } = await supabase
      .from('companies')
      .select('id')
      .eq('slug', slug)
      .single();

    return NextResponse.json({
      available: !existingSlug,
      slug
    });

  } catch (error) {
    console.error('Check slug API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
