import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { verifyAdminAuth } from 'src/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can export audit logs
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const action = searchParams.get('action') || '';
    const resourceType = searchParams.get('resourceType') || '';
    const userId = searchParams.get('userId') || '';
    const companyId = searchParams.get('companyId') || '';
    const startDate = searchParams.get('startDate') || '';
    const endDate = searchParams.get('endDate') || '';
    const format = searchParams.get('format') || 'csv';
    const limit = parseInt(searchParams.get('limit') || '10000');

    const supabase = getSupabaseClient();

    // Build query
    let query = supabase
      .from('audit_logs')
      .select(`
        id,
        created_at,
        user_id,
        user_email,
        company_id,
        action,
        resource_type,
        resource_id,
        old_values,
        new_values,
        ip_address,
        user_agent,
        metadata
      `)
      .order('created_at', { ascending: false })
      .limit(limit);

    // Apply filters
    if (search) {
      query = query.or(`user_email.ilike.%${search}%,action.ilike.%${search}%,resource_type.ilike.%${search}%`);
    }

    if (action) {
      query = query.eq('action', action);
    }

    if (resourceType) {
      query = query.eq('resource_type', resourceType);
    }

    if (userId) {
      query = query.eq('user_id', userId);
    }

    if (companyId) {
      query = query.eq('company_id', companyId);
    }

    if (startDate) {
      query = query.gte('created_at', startDate);
    }

    if (endDate) {
      // Add time to end date to include the entire day
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);
      query = query.lte('created_at', endDateTime.toISOString());
    }

    const { data: auditLogs, error: auditLogsError } = await query;

    if (auditLogsError) {
      console.error('Error fetching audit logs for export:', auditLogsError);
      return NextResponse.json({ error: 'Failed to fetch audit logs' }, { status: 500 });
    }

    if (format === 'json') {
      const jsonData = JSON.stringify(auditLogs, null, 2);
      return new NextResponse(jsonData, {
        headers: {
          'Content-Type': 'application/json',
          'Content-Disposition': `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.json"`
        }
      });
    }

    // Generate CSV
    const headers = [
      'ID',
      'Created At',
      'User ID',
      'User Email',
      'Company ID',
      'Action',
      'Resource Type',
      'Resource ID',
      'IP Address',
      'User Agent',
      'Old Values',
      'New Values',
      'Metadata'
    ];

    const csvRows = [
      headers.join(','),
      ...(auditLogs || []).map(log => [
        log.id,
        log.created_at,
        log.user_id || '',
        `"${log.user_email}"`,
        log.company_id || '',
        log.action,
        log.resource_type,
        log.resource_id || '',
        log.ip_address || '',
        `"${(log.user_agent || '').replace(/"/g, '""')}"`,
        `"${log.old_values ? JSON.stringify(log.old_values).replace(/"/g, '""') : ''}"`,
        `"${log.new_values ? JSON.stringify(log.new_values).replace(/"/g, '""') : ''}"`,
        `"${log.metadata ? JSON.stringify(log.metadata).replace(/"/g, '""') : ''}"`
      ].join(','))
    ];

    const csvContent = csvRows.join('\n');

    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.csv"`
      }
    });

  } catch (error) {
    console.error('Audit logs export API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
